- name: Пе<PERSON><PERSON><PERSON><PERSON><PERSON>н<PERSON>ать сервер (hostname)
  hosts: webservers
  become: true

  tasks:
    - name: Изменить hostname
      hostname:
        name: blue-sky

    - name: Обновить /etc/hosts, чтобы связать localhost и новый hostname
      lineinfile:
        path: /etc/hosts
        regexp: '^127\.0\.1\.1\s+'
        line: "*********   new-hostname"
        state: present

    - name: Перезагрузить сервер (опционально, чтобы применить hostname)
      reboot:
        reboot_timeout: 300
