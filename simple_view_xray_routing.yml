---
- name: Про<PERSON>той просмотр правил маршрутизации Xray
  hosts: webservers
  become: true
  vars:
    xray_config_path: "/usr/local/x-ui/bin/config.json"
    backup_dir: "/root/cert/xray_backups"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Установить jq если не установлен
      apt:
        name: jq
        state: present

    - name: Проверить существование конфигурационного файла Xray
      stat:
        path: "{{ xray_config_path }}"
      register: xray_config_exists

    - name: Завершить если конфигурация не найдена
      fail:
        msg: "Конфигурационный файл Xray не найден: {{ xray_config_path }}"
      when: not xray_config_exists.stat.exists

    - name: Создать директорию для отчетов если не существует
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: "0700"

    - name: Получить общую информацию о конфигурации
      shell: |
        echo "=== ОБЩАЯ ИНФОРМАЦИЯ О КОНФИГУРАЦИИ XRAY ==="
        echo "Файл конфигурации: {{ xray_config_path }}"
        echo "Размер файла: $(stat -c%s {{ xray_config_path }}) байт"
        echo "Дата изменения: $(stat -c%y {{ xray_config_path }})"
        echo ""
        
        if jq -e '.routing' {{ xray_config_path }} > /dev/null 2>&1; then
          echo "Секция routing: ✅ Присутствует"
          TOTAL_RULES=$(jq '.routing.rules | length' {{ xray_config_path }} 2>/dev/null || echo "0")
          echo "Общее количество правил: $TOTAL_RULES"
        else
          echo "Секция routing: ❌ Отсутствует"
        fi
        echo ""
      register: config_info
      changed_when: false

    - name: Показать общую информацию
      debug:
        msg: "{{ config_info.stdout_lines }}"

    - name: Получить все правила маршрутизации
      shell: |
        if jq -e '.routing.rules' {{ xray_config_path }} > /dev/null 2>&1; then
          jq -r '.routing.rules[] | "Правило \(.type // "field"): outboundTag=\(.outboundTag // "default")"' {{ xray_config_path }} 2>/dev/null || echo "Ошибка чтения правил"
        else
          echo "Правила маршрутизации не настроены"
        fi
      register: all_rules
      changed_when: false

    - name: Показать все правила
      debug:
        msg:
          - "=== ВСЕ ПРАВИЛА МАРШРУТИЗАЦИИ ==="
          - "{{ all_rules.stdout_lines }}"

    - name: Получить правила с direct маршрутом
      shell: |
        if jq -e '.routing.rules' {{ xray_config_path }} > /dev/null 2>&1; then
          jq -r '.routing.rules[] | select(.outboundTag == "direct") | "Direct правило: \(.domain // .ip // "unknown")"' {{ xray_config_path }} 2>/dev/null || echo "Нет direct правил"
        else
          echo "Правила маршрутизации не настроены"
        fi
      register: direct_rules
      changed_when: false

    - name: Показать direct правила
      debug:
        msg:
          - "=== ПРАВИЛА С DIRECT МАРШРУТОМ ==="
          - "{{ direct_rules.stdout_lines }}"

    - name: Проверить наличие наших специфических правил
      shell: |
        echo "=== ПРОВЕРКА НАШИХ ПРАВИЛ ==="
        
        # Проверяем geosite:category-gov-ru
        if jq -e '.routing.rules[] | select(.domain[]? == "geosite:category-gov-ru")' {{ xray_config_path }} > /dev/null 2>&1; then
          echo "✅ geosite:category-gov-ru: Настроено"
        else
          echo "❌ geosite:category-gov-ru: Отсутствует"
        fi
        
        # Проверяем regexp:.*\.ru$
        if jq -e '.routing.rules[] | select(.domain[]? == "regexp:.*\\.ru$")' {{ xray_config_path }} > /dev/null 2>&1; then
          echo "✅ regexp:.*\.ru$: Настроено"
        else
          echo "❌ regexp:.*\.ru$: Отсутствует"
        fi
        
        # Проверяем geosite:openai
        if jq -e '.routing.rules[] | select(.domain[]? == "geosite:openai")' {{ xray_config_path }} > /dev/null 2>&1; then
          echo "✅ geosite:openai: Настроено"
        else
          echo "❌ geosite:openai: Отсутствует"
        fi
        
        # Проверяем geoip:ru
        if jq -e '.routing.rules[] | select(.ip[]? == "geoip:ru")' {{ xray_config_path }} > /dev/null 2>&1; then
          echo "✅ geoip:ru: Настроено"
        else
          echo "❌ geoip:ru: Отсутствует"
        fi
      register: our_rules_check
      changed_when: false

    - name: Показать проверку наших правил
      debug:
        msg: "{{ our_rules_check.stdout_lines }}"

    - name: Получить детальную информацию о правилах
      shell: |
        echo "=== ДЕТАЛЬНАЯ ИНФОРМАЦИЯ О ПРАВИЛАХ ==="
        if jq -e '.routing.rules' {{ xray_config_path }} > /dev/null 2>&1; then
          jq -r '.routing.rules[] | 
            "Правило: " + (.type // "field") + 
            "\n  outboundTag: " + (.outboundTag // "default") + 
            (if .domain then "\n  domains: " + (.domain | join(", ")) else "" end) +
            (if .ip then "\n  ips: " + (.ip | join(", ")) else "" end) +
            (if .port then "\n  ports: " + (.port | tostring) else "" end) +
            "\n---"' {{ xray_config_path }} 2>/dev/null || echo "Ошибка чтения детальной информации"
        else
          echo "Правила маршрутизации не настроены"
        fi
      register: detailed_rules
      changed_when: false

    - name: Показать детальную информацию
      debug:
        msg: "{{ detailed_rules.stdout_lines }}"

    - name: Проверить статус x-ui
      systemd:
        name: x-ui
      register: service_status

    - name: Получить статистику правил
      shell: |
        if jq -e '.routing.rules' {{ xray_config_path }} > /dev/null 2>&1; then
          TOTAL=$(jq '.routing.rules | length' {{ xray_config_path }} 2>/dev/null || echo "0")
          DIRECT=$(jq '[.routing.rules[] | select(.outboundTag == "direct")] | length' {{ xray_config_path }} 2>/dev/null || echo "0")
          DOMAIN=$(jq '[.routing.rules[] | select(.domain)] | length' {{ xray_config_path }} 2>/dev/null || echo "0")
          IP=$(jq '[.routing.rules[] | select(.ip)] | length' {{ xray_config_path }} 2>/dev/null || echo "0")
          
          echo "Всего правил: $TOTAL"
          echo "Direct правил: $DIRECT"
          echo "Правил по доменам: $DOMAIN"
          echo "Правил по IP: $IP"
        else
          echo "Всего правил: 0"
          echo "Direct правил: 0"
          echo "Правил по доменам: 0"
          echo "Правил по IP: 0"
        fi
      register: rules_stats
      changed_when: false

    - name: Создать отчет о текущей конфигурации
      copy:
        content: |
          ==========================================
          📊 ОТЧЕТ О МАРШРУТИЗАЦИИ XRAY (ПРОСТОЙ)
          ==========================================
          
          📅 Дата анализа: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          📁 Конфигурация: {{ xray_config_path }}
          
          ==========================================
          📈 СТАТИСТИКА
          ==========================================
          
          {{ rules_stats.stdout }}
          
          ==========================================
          🔧 СТАТУС СЕРВИСА
          ==========================================
          
          Статус x-ui: {{ service_status.status.ActiveState }}
          Включен при загрузке: {{ service_status.status.UnitFileState }}
          
          ==========================================
          📋 ОБЩАЯ ИНФОРМАЦИЯ
          ==========================================
          
          {{ config_info.stdout }}
          
          ==========================================
          ✅ ПРОВЕРКА НАШИХ ПРАВИЛ
          ==========================================
          
          {{ our_rules_check.stdout }}
          
          ==========================================
          📖 ВСЕ ПРАВИЛА МАРШРУТИЗАЦИИ
          ==========================================
          
          {{ all_rules.stdout }}
          
          ==========================================
          🛠️ УПРАВЛЕНИЕ
          ==========================================
          
          Для добавления правил:
          ansible-playbook -i inventory.ini configure_xray_routing.yml
          
          Для просмотра JSON:
          jq '.routing.rules' {{ xray_config_path }}
          
          Для резервного копирования:
          cp {{ xray_config_path }} {{ backup_dir }}/config.json.manual.$(date +%s)
          
          ==========================================
        dest: "{{ backup_dir }}/SIMPLE_ROUTING_REPORT.txt"
        mode: "0644"

    - name: Показать краткую сводку
      debug:
        msg:
          - "📊 Простой анализ маршрутизации Xray завершен"
          - ""
          - "📈 Статистика:"
          - "{{ rules_stats.stdout_lines }}"
          - ""
          - "🔧 Статус x-ui: {{ service_status.status.ActiveState }}"
          - "📁 Конфигурация: {{ xray_config_path }}"
          - "📋 Отчет: {{ backup_dir }}/SIMPLE_ROUTING_REPORT.txt"
          - ""
          - "✅ Проверка наших правил:"
          - "{{ our_rules_check.stdout_lines }}"
          - ""
          - "💡 Для добавления правил:"
          - "   ansible-playbook -i inventory.ini configure_xray_routing.yml"
