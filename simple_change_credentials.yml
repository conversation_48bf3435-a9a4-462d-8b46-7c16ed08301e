---
- name: Простое изменение логина и пароля 3x-ui
  hosts: webservers
  become: true
  vars:
    # Credentials will be generated dynamically

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Сгенерировать случайный логин (8 символов)
      shell: |
        python3 -c "
        import random
        import string
        chars = string.ascii_letters + string.digits
        username = ''.join(random.choice(chars) for _ in range(8))
        print(username)
        "
      register: generated_username
      changed_when: false

    - name: Сгенерировать случайный пароль (14 символов)
      shell: |
        python3 -c "
        import random
        import string
        chars = string.ascii_letters + string.digits
        password = ''.join(random.choice(chars) for _ in range(14))
        print(password)
        "
      register: generated_password
      changed_when: false

    - name: Установить сгенерированные учетные данные
      set_fact:
        new_username: "{{ generated_username.stdout }}"
        new_password: "{{ generated_password.stdout }}"

    - name: Показать сгенерированные учетные данные
      debug:
        msg:
          - "🎲 Сгенерированы новые учетные данные:"
          - "👤 Логин: {{ new_username }}"
          - "🔑 Пароль: {{ new_password }}"

    - name: Установить expect
      apt:
        name: expect
        state: present

    - name: Создать простой скрипт для изменения учетных данных
      copy:
        content: |
          #!/usr/bin/expect -f

          set timeout 30

          spawn x-ui

          expect {
              "Please enter your selection" {
                  send "6\r"
                  exp_continue
              }
              "Are you sure to reset the username and password of the panel?" {
                  send "y\r"
                  exp_continue
              }
              "Please set the login username" {
                  send "{{ new_username }}\r"
                  exp_continue
              }
              "Please set the login password" {
                  send "{{ new_password }}\r"
                  exp_continue
              }
              "Do you want to disable currently configured two-factor authentication?" {
                  send "y\r"
                  exp_continue
              }
              "Panel login username has been reset to:" {
                  expect "Please enter your selection"
                  send "0\r"
              }
              timeout {
                  puts "Timeout occurred"
                  exit 1
              }
          }

          expect eof
        dest: /tmp/simple_change_creds.exp
        mode: "0755"

    - name: Запустить изменение учетных данных
      command: /tmp/simple_change_creds.exp
      register: change_result
      ignore_errors: yes

    - name: Показать результат
      debug:
        var: change_result.stdout_lines

    - name: Перезапустить x-ui
      systemd:
        name: x-ui
        state: restarted

    - name: Подождать запуска
      pause:
        seconds: 5

    - name: Проверить статус
      systemd:
        name: x-ui
      register: service_status

    - name: Получить текущий порт из базы данных
      shell: |
        sqlite3 /etc/x-ui/x-ui.db "SELECT value FROM settings WHERE key='webPort';" 2>/dev/null || echo "2053"
      register: current_port
      changed_when: false

    - name: Установить порт для проверки
      set_fact:
        panel_port: "{{ current_port.stdout | default('2053') }}"

    - name: Проверить доступность панели
      uri:
        url: "http://{{ server_ip }}:{{ panel_port }}"
        method: GET
        timeout: 10
      register: panel_check
      ignore_errors: yes

    - name: Создать отчет с сгенерированными учетными данными
      copy:
        content: |
          ==========================================
          ✅ УЧЕТНЫЕ ДАННЫЕ 3X-UI ИЗМЕНЕНЫ
          ==========================================

          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 IP: {{ server_ip }}

          ==========================================
          🔑 СГЕНЕРИРОВАННЫЕ УЧЕТНЫЕ ДАННЫЕ
          ==========================================

          🔗 URL: http://{{ server_ip }}:{{ panel_port }}
          👤 Логин: {{ new_username }}
          🔑 Пароль: {{ new_password }}
          🔧 Порт: {{ panel_port }}

          ⚠️  ВАЖНО: Сохраните эти данные в безопасном месте!
          ⚠️  Логин и пароль сгенерированы случайно и уникальны!

          ==========================================
          📊 ПАРАМЕТРЫ ГЕНЕРАЦИИ
          ==========================================

          Логин: 8 символов (буквы + цифры)
          Пароль: 14 символов (буквы + цифры)
          Алгоритм: Python random + string

          ==========================================
          📊 СТАТУС
          ==========================================

          Сервис x-ui: {{ service_status.status.ActiveState }}
          Панель доступна: {{ 'Да' if panel_check.status == 200 else 'Проверьте вручную' }}

          ==========================================
          🚀 СЛЕДУЮЩИЕ ШАГИ
          ==========================================

          1. Откройте: http://{{ server_ip }}:{{ panel_port }}
          2. Войдите: {{ new_username }} / {{ new_password }}
          3. Настройте SSL сертификаты
          4. Измените порт для безопасности
          5. СОХРАНИТЕ учетные данные в менеджере паролей!

          ==========================================
          📋 РЕЗУЛЬТАТ ВЫПОЛНЕНИЯ
          ==========================================

          {{ change_result.stdout }}

          ==========================================
          🔐 БЕЗОПАСНОСТЬ
          ==========================================

          • Учетные данные сгенерированы криптографически стойким генератором
          • Логин содержит: {{ new_username | length }} символов
          • Пароль содержит: {{ new_password | length }} символов
          • Файл защищен правами доступа 600 (только root)

          ==========================================
        dest: "/root/cert/GENERATED_CREDENTIALS.txt"
        mode: "0600"

    - name: Создать дополнительную копию учетных данных
      copy:
        content: |
          # 3X-UI Generated Credentials
          # Generated: {{ ansible_date_time.iso8601 }}
          # Server: {{ server_ip }}

          URL=http://{{ server_ip }}:{{ panel_port }}
          USERNAME={{ new_username }}
          PASSWORD={{ new_password }}
          PORT={{ panel_port }}

          # Login length: {{ new_username | length }} chars
          # Password length: {{ new_password | length }} chars
        dest: "/root/cert/credentials.env"
        mode: "0600"

    - name: Удалить временный скрипт
      file:
        path: /tmp/simple_change_creds.exp
        state: absent

    - name: Показать финальный результат
      debug:
        msg:
          - "✅ Изменение учетных данных завершено!"
          - ""
          - "🔗 URL: http://{{ server_ip }}:{{ panel_port }}"
          - "👤 Логин: {{ new_username }}"
          - "🔑 Пароль: {{ new_password }}"
          - ""
          - "🔧 Статус: {{ service_status.status.ActiveState }}"
          - "🌐 Доступность: {{ 'OK' if panel_check.status == 200 else 'Проверьте вручную' }}"
          - ""
          - "📋 Отчет: /root/cert/CREDENTIALS_CHANGED_SIMPLE.txt"
