- name: <PERSON><PERSON><PERSON><PERSON><PERSON>ить BBR TCP congestion control
  hosts: webservers
  become: true

  tasks:
    - name: Загрузить модуль tcp_bbr
      modprobe:
        name: tcp_bbr
        state: present

    - name: Добавить настройку default_qdisc = fq
      sysctl:
        name: net.core.default_qdisc
        value: fq
        state: present
        reload: yes

    - name: Добавить настройку tcp_congestion_control = bbr
      sysctl:
        name: net.ipv4.tcp_congestion_control
        value: bbr
        state: present
        reload: yes

    - name: Проверить, что BBR включен
      shell: sysctl net.ipv4.tcp_congestion_control
      register: bbr_status

    - debug:
        msg: "BBR status: {{ bbr_status.stdout }}"
