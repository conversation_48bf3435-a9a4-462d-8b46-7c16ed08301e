- name: Создать пользователя с правами sudo
  hosts: webservers
  become: true

  vars:
    username: "cass"
    password: "{{ 'qwe' | password_hash('sha512') }}"

  tasks:
    - name: Создать пользователя
      user:
        name: "{{ username }}"
        password: "{{ password }}"
        shell: /bin/bash
        groups: sudo
        append: yes
        create_home: yes

    - name: Создать .ssh директорию
      file:
        path: "/home/<USER>/.ssh"
        state: directory
        owner: "{{ username }}"
        group: "{{ username }}"
        mode: "0700"

    - name: Скопировать публичный ключ
      copy:
        src: "~/.ssh/id_rsa.pub"
        dest: "/home/<USER>/.ssh/authorized_keys"
        owner: "{{ username }}"
        group: "{{ username }}"
        mode: "0600"
