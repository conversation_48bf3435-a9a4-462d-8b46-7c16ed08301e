---
- name: Настроить 3x-ui панель с SSL сертификатами
  hosts: webservers
  become: true
  vars:
    # Настройки панели
    panel_port: 8443 # Новый порт панели (можно изменить)
    panel_username: "admin_{{ ansible_date_time.epoch }}" # Уникальный логин
    panel_password: "{{ lookup('password', '/tmp/3x_ui_password chars=ascii_letters,digits length=16') }}" # Случайный пароль
    panel_uuid: "{{ ansible_date_time.iso8601_micro | to_uuid }}" # Генерируем UUID

    # Пути к сертификатам
    cert_dir: "/root/cert"
    cert_public_key: "{{ cert_dir }}/cert.crt"
    cert_private_key: "{{ cert_dir }}/secret.key"

    # База данных 3x-ui
    x_ui_db_path: "/etc/x-ui/x-ui.db"

    # Настройки SSL
    enable_ssl: true
    ssl_cert_file: "{{ cert_public_key }}"
    ssl_key_file: "{{ cert_private_key }}"

  tasks:
    - name: Установить sqlite3 для работы с базой данных
      apt:
        name: sqlite3
        state: present
        update_cache: yes

    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Остановить сервис x-ui для безопасного изменения настроек
      systemd:
        name: x-ui
        state: stopped
      ignore_errors: yes

    - name: Проверить существование базы данных x-ui
      stat:
        path: "{{ x_ui_db_path }}"
      register: db_exists

    - name: Создать резервную копию базы данных
      copy:
        src: "{{ x_ui_db_path }}"
        dest: "{{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}"
        remote_src: yes
      when: db_exists.stat.exists

    - name: Проверить существование сертификатов
      stat:
        path: "{{ item }}"
      register: cert_files
      loop:
        - "{{ ssl_cert_file }}"
        - "{{ ssl_key_file }}"

    - name: Убедиться, что сертификаты существуют
      fail:
        msg: "SSL сертификат {{ item.item }} не найден! Сначала запустите create_ssl_cert_manual.yml"
      when: not item.stat.exists
      loop: "{{ cert_files.results }}"

    - name: Сгенерировать новый UUID для панели
      set_fact:
        generated_uuid: "{{ 999999999 | random | to_uuid }}"

    - name: Получить структуру базы данных
      shell: sqlite3 {{ x_ui_db_path }} ".schema"
      register: db_schema
      when: db_exists.stat.exists

    - name: Показать структуру базы данных (для отладки)
      debug:
        var: db_schema.stdout_lines
      when: db_exists.stat.exists

    - name: Обновить порт панели
      shell: |
        sqlite3 {{ x_ui_db_path }} "INSERT OR REPLACE INTO settings (key, value) VALUES ('webPort', '{{ panel_port }}');"
      when: db_exists.stat.exists

    - name: Обновить SSL сертификат
      shell: |
        sqlite3 {{ x_ui_db_path }} "INSERT OR REPLACE INTO settings (key, value) VALUES ('webCertFile', '{{ ssl_cert_file }}');"
      when: db_exists.stat.exists

    - name: Обновить SSL ключ
      shell: |
        sqlite3 {{ x_ui_db_path }} "INSERT OR REPLACE INTO settings (key, value) VALUES ('webKeyFile', '{{ ssl_key_file }}');"
      when: db_exists.stat.exists

    - name: Обновить адрес прослушивания
      shell: |
        sqlite3 {{ x_ui_db_path }} "INSERT OR REPLACE INTO settings (key, value) VALUES ('webListen', '');"
      when: db_exists.stat.exists

    - name: Обновить базовый путь панели
      shell: |
        sqlite3 {{ x_ui_db_path }} "INSERT OR REPLACE INTO settings (key, value) VALUES ('webBasePath', '/');"
      when: db_exists.stat.exists

    - name: Получить хеш пароля
      shell: |
        python3 -c "
        import hashlib
        password = '{{ panel_password }}'
        hashed = hashlib.md5(password.encode()).hexdigest()
        print(hashed)
        "
      register: password_hash

    - name: Обновить учетные данные администратора
      shell: |
        sqlite3 {{ x_ui_db_path }} "UPDATE users SET username = '{{ panel_username }}', password = '{{ password_hash.stdout }}' WHERE id = 1;"
      when: db_exists.stat.exists

    - name: Создать скрипт для настройки панели через x-ui команду
      copy:
        content: |
          #!/bin/bash
          # Скрипт автоматической настройки 3x-ui панели

          # Функция для отправки команд в x-ui
          configure_panel() {
              expect << EOF
          spawn x-ui
          expect "Please choose an option:"
          send "8\r"
          expect "Please set up the panel port:"
          send "{{ panel_port }}\r"
          expect "Please set up the panel username:"
          send "{{ panel_username }}\r"
          expect "Please set up the panel password:"
          send "{{ panel_password }}\r"
          expect "Please set up the panel path:"
          send "/\r"
          expect "Please choose an option:"
          send "11\r"
          expect "Please set the panel certificate file path:"
          send "{{ ssl_cert_file }}\r"
          expect "Please set the panel key file path:"
          send "{{ ssl_key_file }}\r"
          expect "Please choose an option:"
          send "0\r"
          expect eof
          EOF
          }

          # Установить expect если не установлен
          apt-get update && apt-get install -y expect

          # Запустить настройку
          configure_panel

          # Перезапустить сервис
          systemctl restart x-ui
          systemctl enable x-ui
        dest: /tmp/configure_3x_ui.sh
        mode: "0755"

    - name: Установить expect для автоматизации
      apt:
        name: expect
        state: present

    - name: Запустить скрипт настройки панели
      shell: /tmp/configure_3x_ui.sh
      register: config_result
      ignore_errors: yes

    - name: Запустить сервис x-ui
      systemd:
        name: x-ui
        state: started
        enabled: yes
        daemon_reload: yes

    - name: Подождать запуска сервиса
      wait_for:
        port: "{{ panel_port }}"
        host: "{{ server_ip }}"
        delay: 5
        timeout: 30
      ignore_errors: yes

    - name: Создать файл с учетными данными
      copy:
        content: |
          ===========================================
          3X-UI ПАНЕЛЬ НАСТРОЕНА УСПЕШНО
          ===========================================

          🌐 IP адрес сервера: {{ server_ip }}
          🔗 URL панели: https://{{ server_ip }}:{{ panel_port }}
          👤 Логин: {{ panel_username }}
          🔑 Пароль: {{ panel_password }}
          🆔 UUID: {{ generated_uuid }}

          ===========================================
          SSL СЕРТИФИКАТЫ
          ===========================================

          📁 Директория сертификатов: {{ cert_dir }}
          🔐 Публичный ключ: {{ ssl_cert_file }}
          🔑 Приватный ключ: {{ ssl_key_file }}

          ===========================================
          ВАЖНАЯ ИНФОРМАЦИЯ
          ===========================================

          ⚠️  СОХРАНИТЕ ЭТИ ДАННЫЕ В БЕЗОПАСНОМ МЕСТЕ!
          ⚠️  Стандартные учетные данные (admin/admin) ИЗМЕНЕНЫ!

          🔄 Для сброса настроек используйте команду: x-ui
          📋 Резервная копия БД: {{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}

          ===========================================
          СЛЕДУЮЩИЕ ШАГИ
          ===========================================

          1. Откройте браузер и перейдите по адресу:
             https://{{ server_ip }}:{{ panel_port }}

          2. Войдите используя новые учетные данные:
             Логин: {{ panel_username }}
             Пароль: {{ panel_password }}

          3. Проверьте настройки SSL в панели управления

          4. Настройте правила фаервола для порта {{ panel_port }}

          ===========================================
        dest: "{{ cert_dir }}/PANEL_CREDENTIALS.txt"
        mode: "0600"
        owner: root
        group: root

    - name: Показать финальную информацию
      debug:
        msg:
          - "🎉 3x-ui панель успешно настроена!"
          - "🌐 URL: https://{{ server_ip }}:{{ panel_port }}"
          - "👤 Логин: {{ panel_username }}"
          - "🔑 Пароль: {{ panel_password }}"
          - "📁 Учетные данные сохранены в: {{ cert_dir }}/PANEL_CREDENTIALS.txt"
          - ""
          - "⚠️  ВАЖНО: Сохраните учетные данные в безопасном месте!"
          - "⚠️  Стандартные admin/admin больше НЕ РАБОТАЮТ!"

    - name: Проверить статус сервиса
      systemd:
        name: x-ui
      register: service_status

    - name: Показать статус сервиса
      debug:
        msg: "Статус сервиса x-ui: {{ service_status.status.ActiveState }}"

    - name: Удалить временные файлы
      file:
        path: /tmp/configure_3x_ui.sh
        state: absent
