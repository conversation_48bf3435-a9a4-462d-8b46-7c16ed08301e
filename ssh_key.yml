- name: Установка ssh-ключа
  hosts: webservers
  become: true

  vars:
    my_ssh_key: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC2QTNUp+AvbK92M9ErCUNn/qLf91XzPQ/l4rTQ/hBbyO/RYbhMX2J4YIta7b8BjHv25oY3nQ2ECu48+TxW9IX4fU+9V4YqizeqNeZ5ABNL7FTE2Fv9a+XxBB6MsPECUN4V8ElELWexUc3F9Rg9IvXysrqFVPdzm1lM2eULVwT+DwFGXPWYoma5IF7pJLahCf3Sj6f8XnQ0jBJkLRNV/fPFw4xtaDZJ2tOnZj0tlAbhjvDe0tzkRJY+4KbrYrScbCIJ9+9TS08w0QXE0xACJSZ/ZlNLVYVhMmWHzlTQ7kkj7Xesvosru4pOZoiNi8Ebb00glbVi2X/P4aBHjN2jGOd0Rh93Sfb1cTopsEm+2Ig1ed2xfEo/yi3jjeqflvVwn5CjUi/wiDgy+Opq+gpv50DknlZbSo/9uwsM4XcQu9odGShE1HUcGB9VPS/hZw/l8ZlkHoOESAMcSyWR2Ah/l6YVWfP0pGO7a3QPw3Xd0caY0GkTXA7fhiRsaqPdr5NZkHs= cass@cass-Inspiron-15-7000-Gaming%"

  tasks:
    - name: Проверяем, есть ли ключ в authorized_keys
      ansible.builtin.shell: grep -F "{{ my_ssh_key }}" /root/.ssh/authorized_keys || true
      register: key_check
      changed_when: false
      failed_when: false

    - name: Создаем папку .ssh, если нужно
      file:
        path: /root/.ssh
        state: directory
        mode: "0700"
        owner: root
        group: root

    - name: Добавляем ключ, если его нет
      authorized_key:
        user: root
        key: "{{ my_ssh_key }}"
        state: present
      when: key_check.stdout == ""

    - name: Отключаем вход по паролю, если ключ добавлен или уже был
      lineinfile:
        path: /etc/ssh/sshd_config
        regexp: '^#?PasswordAuthentication\s+'
        line: "PasswordAuthentication no"
        backrefs: yes
      when: key_check.stdout == "" or
        key_check.stdout != ""

    - name: Включаем PermitRootLogin, если нужно
      lineinfile:
        path: /etc/ssh/sshd_config
        regexp: '^#?PermitRootLogin\s+'
        line: "PermitRootLogin yes"
        backrefs: yes

    - name: Перезапускаем SSH
      service:
        name: ssh
        state: restarted
      when: key_check.stdout == "" or
        key_check.stdout != ""
