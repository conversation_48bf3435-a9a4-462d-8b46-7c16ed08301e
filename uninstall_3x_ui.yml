- name: Удалить 3x-ui
  hosts: webservers
  become: true
  tasks:
    - name: Остановить сервис x-ui
      systemd:
        name: x-ui
        state: stopped
        enabled: no
      ignore_errors: yes

    - name: Удалить systemd unit
      file:
        path: /etc/systemd/system/x-ui.service
        state: absent
      ignore_errors: yes

    - name: Удалить бинарники и папки x-ui
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /usr/local/x-ui
        - /usr/bin/x-ui

    - name: Перезагрузить systemd демона
      command: systemctl daemon-reload
      changed_when: false
