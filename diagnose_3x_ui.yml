---
- name: Диагностика 3x-ui панели и базы данных
  hosts: webservers
  become: true
  vars:
    x_ui_db_path: "/etc/x-ui/x-ui.db"
    cert_dir: "/root/cert"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Проверить статус сервиса x-ui
      systemd:
        name: x-ui
      register: service_status

    - name: Показать статус сервиса
      debug:
        msg: "Статус x-ui: {{ service_status.status.ActiveState }}"

    - name: Проверить существование базы данных
      stat:
        path: "{{ x_ui_db_path }}"
      register: db_exists

    - name: Показать информацию о базе данных
      debug:
        msg:
          - "База данных существует: {{ db_exists.stat.exists }}"
          - "Размер файла: {{ db_exists.stat.size | default('N/A') }} байт"
          - "Права доступа: {{ db_exists.stat.mode | default('N/A') }}"

    - name: Проверить структуру базы данных
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .tables
        EOF
      register: db_tables
      when: db_exists.stat.exists

    - name: Показать таблицы в базе данных
      debug:
        msg: "Таблицы в БД: {{ db_tables.stdout_lines }}"
      when: db_exists.stat.exists

    - name: Получить все настройки из базы данных
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .headers on
        .mode column
        .width 15 50
        SELECT key, value FROM settings ORDER BY key;
        EOF
      register: all_settings
      when: db_exists.stat.exists

    - name: Показать все настройки
      debug:
        msg:
          - "Настройки в базе данных:"
          - "{{ all_settings.stdout_lines | join('\n') }}"
      when: db_exists.stat.exists

    - name: Получить информацию о пользователях
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .headers on
        .mode column
        SELECT id, username, CASE WHEN length(password) > 10 THEN substr(password,1,10)||'...' ELSE password END as password_preview FROM users;
        EOF
      register: users_info
      when: db_exists.stat.exists

    - name: Показать пользователей
      debug:
        msg:
          - "Пользователи в системе:"
          - "{{ users_info.stdout_lines | join('\n') }}"
      when: db_exists.stat.exists

    - name: Проверить дублированные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        SELECT key, COUNT(*) as count FROM settings GROUP BY key HAVING COUNT(*) > 1;
        EOF
      register: duplicates
      when: db_exists.stat.exists

    - name: Показать дублированные настройки
      debug:
        msg: "Дублированные настройки: {{ duplicates.stdout_lines if duplicates.stdout_lines else 'Нет дублей' }}"
      when: db_exists.stat.exists

    - name: Проверить SSL сертификаты
      stat:
        path: "{{ item }}"
      register: cert_files
      loop:
        - "{{ cert_dir }}/cert.crt"
        - "{{ cert_dir }}/secret.key"

    - name: Показать статус сертификатов
      debug:
        msg: "{{ item.item }}: {{ 'Существует' if item.stat.exists else 'НЕ НАЙДЕН' }}"
      loop: "{{ cert_files.results }}"

    - name: Проверить открытые порты
      shell: netstat -tlnp | grep x-ui || echo "x-ui не слушает порты"
      register: open_ports
      changed_when: false

    - name: Показать открытые порты
      debug:
        msg:
          - "Открытые порты x-ui:"
          - "{{ open_ports.stdout_lines | join('\n') }}"

    - name: Получить логи x-ui
      shell: journalctl -u x-ui --no-pager -n 20
      register: x_ui_logs
      changed_when: false

    - name: Показать последние логи x-ui
      debug:
        msg:
          - "Последние 20 строк логов x-ui:"
          - "{{ x_ui_logs.stdout_lines | join('\n') }}"

    - name: Проверить конфигурационные файлы
      find:
        paths:
          - /etc/x-ui
          - /usr/local/x-ui
        patterns: "*.json,*.conf,*.cfg"
        recurse: yes
      register: config_files

    - name: Показать найденные конфигурационные файлы
      debug:
        msg: "Конфигурационные файлы: {{ config_files.files | map(attribute='path') | list }}"

    - name: Создать диагностический отчет
      copy:
        content: |
          ==========================================
          🔍 ДИАГНОСТИКА 3X-UI ПАНЕЛИ
          ==========================================

          📅 Дата проверки: {{ ansible_date_time.iso8601 }}
          🌐 IP сервера: {{ server_ip }}

          ==========================================
          🔧 СТАТУС СЕРВИСА
          ==========================================

          Статус x-ui: {{ service_status.status.ActiveState }}
          Включен при загрузке: {{ service_status.status.UnitFileState }}

          ==========================================
          💾 БАЗА ДАННЫХ
          ==========================================

          Путь: {{ x_ui_db_path }}
          Существует: {{ db_exists.stat.exists }}
          {% if db_exists.stat.exists %}
          Размер: {{ db_exists.stat.size }} байт
          Права: {{ db_exists.stat.mode }}

          Таблицы: {{ db_tables.stdout }}
          {% endif %}

          ==========================================
          ⚙️  НАСТРОЙКИ В БАЗЕ ДАННЫХ
          ==========================================

          {% if db_exists.stat.exists %}
          {{ all_settings.stdout }}
          {% else %}
          База данных не найдена!
          {% endif %}

          ==========================================
          👥 ПОЛЬЗОВАТЕЛИ
          ==========================================

          {% if db_exists.stat.exists %}
          {{ users_info.stdout }}
          {% else %}
          База данных не найдена!
          {% endif %}

          ==========================================
          🔐 SSL СЕРТИФИКАТЫ
          ==========================================

          {% for item in cert_files.results %}
          {{ item.item }}: {{ 'Существует' if item.stat.exists else 'НЕ НАЙДЕН' }}
          {% endfor %}

          ==========================================
          🌐 СЕТЕВЫЕ ПОДКЛЮЧЕНИЯ
          ==========================================

          {{ open_ports.stdout }}

          ==========================================
          📋 ПРОБЛЕМЫ И РЕКОМЕНДАЦИИ
          ==========================================

          {% if not db_exists.stat.exists %}
          ❌ База данных не найдена - переустановите 3x-ui
          {% endif %}

          {% if duplicates.stdout_lines %}
          ⚠️  Найдены дублированные настройки:
          {{ duplicates.stdout }}
          Рекомендация: Запустите fix_3x_ui_database.yml
          {% endif %}

          {% if service_status.status.ActiveState != 'active' %}
          ❌ Сервис x-ui не запущен
          Команда для запуска: systemctl start x-ui
          {% endif %}

          {% for item in cert_files.results %}
          {% if not item.stat.exists %}
          ❌ SSL сертификат не найден: {{ item.item }}
          {% endif %}
          {% endfor %}

          ==========================================
          🔧 КОМАНДЫ ДЛЯ ИСПРАВЛЕНИЯ
          ==========================================

          # Перезапустить x-ui
          systemctl restart x-ui

          # Проверить статус
          systemctl status x-ui

          # Посмотреть логи
          journalctl -u x-ui -f

          # Настроить панель вручную
          x-ui

          # Исправить базу данных через Ansible
          ansible-playbook -i inventory.ini fix_3x_ui_database.yml

          ==========================================
        dest: "{{ cert_dir }}/DIAGNOSTIC_REPORT.txt"
        mode: "0644"

    - name: Показать краткую сводку
      debug:
        msg:
          - "🔍 Диагностика завершена"
          - ""
          - "🔧 Статус x-ui: {{ service_status.status.ActiveState }}"
          - "💾 База данных: {{ 'Найдена' if db_exists.stat.exists else 'НЕ НАЙДЕНА' }}"
          - "🔐 SSL сертификаты: {{ cert_files.results | selectattr('stat.exists') | list | length }}/{{ cert_files.results | length }}"
          - ""
          - "📋 Полный отчет: {{ cert_dir }}/DIAGNOSTIC_REPORT.txt"
          - ""
          - "{% if duplicates.stdout_lines %}⚠️  Найдены дублированные настройки - запустите fix_3x_ui_database.yml{% endif %}"
