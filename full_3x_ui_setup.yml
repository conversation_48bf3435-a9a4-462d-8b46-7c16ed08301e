---
- name: Пол<PERSON>я установка и настройка 3x-ui с SSL
  hosts: webservers
  become: true
  vars:
    # Настройки панели
    panel_port: 8443
    panel_username: "admin_secure"
    panel_password: "{{ lookup('password', '/tmp/3x_ui_password chars=ascii_letters,digits length=16') }}"

    # Пути к сертификатам
    cert_dir: "/root/cert"
    cert_validity_days: 365

    # База данных 3x-ui
    x_ui_db_path: "/etc/x-ui/x-ui.db"

  tasks:
    # ========== УСТАНОВКА 3X-UI ==========
    - name: Обновить пакеты системы
      apt:
        update_cache: yes
        upgrade: dist

    - name: Установить необходимые пакеты
      apt:
        name:
          - curl
          - wget
          - unzip
          - openssl
          - sqlite3
          - python3
        state: present

    - name: Установить 3x-ui панель
      shell: |
        echo "n" | bash <(curl -Ls https://raw.githubusercontent.com/mhsanaei/3x-ui/master/install.sh)
      args:
        executable: /bin/bash

    # ========== СОЗДАНИЕ SSL СЕРТИФИКАТОВ ==========
    - name: Создать директорию для сертификатов
      file:
        path: "{{ cert_dir }}"
        state: directory
        mode: "0700"
        owner: root
        group: root

    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать закрытый ключ
      command: openssl genrsa -out secret.key 2048
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/secret.key"

    - name: Установить права на закрытый ключ
      file:
        path: "{{ cert_dir }}/secret.key"
        mode: "0600"
        owner: root
        group: root

    - name: Создать конфигурационный файл для CSR
      copy:
        content: |
          [req]
          distinguished_name = req_distinguished_name
          req_extensions = v3_req
          prompt = no

          [req_distinguished_name]
          C = RU
          ST = Moscow
          L = Moscow
          O = Private
          OU = IT
          CN = {{ server_ip }}
          emailAddress = admin@{{ server_ip }}

          [v3_req]
          keyUsage = keyEncipherment, dataEncipherment
          extendedKeyUsage = serverAuth
          subjectAltName = @alt_names

          [alt_names]
          IP.1 = {{ server_ip }}
        dest: "{{ cert_dir }}/openssl.conf"
        mode: "0644"

    - name: Создать запрос на подпись сертификата
      command: openssl req -key secret.key -new -out cert.csr -config openssl.conf
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/cert.csr"

    - name: Создать самоподписанный сертификат
      command: openssl x509 -signkey secret.key -in cert.csr -req -days {{ cert_validity_days }} -out cert.crt -extensions v3_req -extfile openssl.conf
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/cert.crt"

    - name: Установить права на сертификат
      file:
        path: "{{ cert_dir }}/cert.crt"
        mode: "0644"
        owner: root
        group: root

    # ========== НАСТРОЙКА 3X-UI ПАНЕЛИ ==========
    - name: Остановить сервис x-ui
      systemd:
        name: x-ui
        state: stopped
      ignore_errors: yes

    - name: Подождать остановки сервиса
      pause:
        seconds: 3

    - name: Проверить существование базы данных
      stat:
        path: "{{ x_ui_db_path }}"
      register: db_exists

    - name: Создать резервную копию базы данных
      copy:
        src: "{{ x_ui_db_path }}"
        dest: "{{ x_ui_db_path }}.backup"
        remote_src: yes
      when: db_exists.stat.exists

    - name: Получить хеш пароля
      shell: |
        python3 -c "
        import hashlib
        password = '{{ panel_password }}'
        hashed = hashlib.md5(password.encode()).hexdigest()
        print(hashed)
        "
      register: password_hash

    - name: Очистить дублированные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        DELETE FROM settings WHERE id NOT IN (
          SELECT MAX(id) FROM settings GROUP BY key
        );
        EOF
      when: db_exists.stat.exists

    - name: Настроить панель через базу данных
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        -- Удалить старые настройки
        DELETE FROM settings WHERE key IN ('webPort', 'webCertFile', 'webKeyFile', 'webListen', 'webBasePath');

        -- Вставить новые настройки
        INSERT INTO settings (key, value) VALUES ('webPort', '{{ panel_port }}');
        INSERT INTO settings (key, value) VALUES ('webCertFile', '{{ cert_dir }}/cert.crt');
        INSERT INTO settings (key, value) VALUES ('webKeyFile', '{{ cert_dir }}/secret.key');
        INSERT INTO settings (key, value) VALUES ('webListen', '');
        INSERT INTO settings (key, value) VALUES ('webBasePath', '/');

        -- Обновить пользователя
        UPDATE users SET username = '{{ panel_username }}', password = '{{ password_hash.stdout }}' WHERE id = 1;
        EOF
      when: db_exists.stat.exists

    - name: Проверить применение настроек
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .headers on
        .mode column
        SELECT key, value FROM settings WHERE key IN ('webPort', 'webCertFile', 'webKeyFile', 'webListen', 'webBasePath');
        EOF
      register: applied_settings
      when: db_exists.stat.exists

    - name: Показать примененные настройки
      debug:
        msg: "Настройки в базе данных:"
        var: applied_settings.stdout_lines
      when: db_exists.stat.exists

    - name: Запустить сервис x-ui
      systemd:
        name: x-ui
        state: started
        enabled: yes
        daemon_reload: yes

    - name: Подождать запуска панели
      wait_for:
        port: "{{ panel_port }}"
        host: "{{ server_ip }}"
        delay: 5
        timeout: 30
      ignore_errors: yes

    # ========== СОЗДАНИЕ ОТЧЕТА ==========
    - name: Создать файл с учетными данными
      copy:
        content: |
          ==========================================
          🎉 3X-UI ПАНЕЛЬ НАСТРОЕНА УСПЕШНО
          ==========================================

          🌐 IP адрес сервера: {{ server_ip }}
          🔗 URL панели: https://{{ server_ip }}:{{ panel_port }}
          👤 Логин: {{ panel_username }}
          🔑 Пароль: {{ panel_password }}

          ==========================================
          📋 SSL СЕРТИФИКАТЫ
          ==========================================

          📁 Директория: {{ cert_dir }}
          🔐 Публичный ключ: {{ cert_dir }}/cert.crt
          🔑 Приватный ключ: {{ cert_dir }}/secret.key
          📅 Срок действия: {{ cert_validity_days }} дней

          ==========================================
          ⚠️  ВАЖНАЯ ИНФОРМАЦИЯ
          ==========================================

          • Стандартные учетные данные (admin/admin) ИЗМЕНЕНЫ
          • Сохраните эти данные в безопасном месте
          • Резервная копия БД: {{ x_ui_db_path }}.backup
          • Для сброса настроек используйте: x-ui

          ==========================================
          🚀 СЛЕДУЮЩИЕ ШАГИ
          ==========================================

          1. Откройте браузер: https://{{ server_ip }}:{{ panel_port }}
          2. Войдите с новыми учетными данными
          3. Настройте правила фаервола для порта {{ panel_port }}
          4. Создайте пользователей и настройте подключения

          ==========================================
        dest: "{{ cert_dir }}/SETUP_COMPLETE.txt"
        mode: "0600"

    - name: Показать финальную информацию
      debug:
        msg:
          - "🎉 Установка завершена успешно!"
          - ""
          - "🌐 Панель доступна: https://{{ server_ip }}:{{ panel_port }}"
          - "👤 Логин: {{ panel_username }}"
          - "🔑 Пароль: {{ panel_password }}"
          - ""
          - "📁 Отчет сохранен: {{ cert_dir }}/SETUP_COMPLETE.txt"
          - ""
          - "⚠️  ВАЖНО: Сохраните учетные данные!"

    - name: Проверить статус сервиса
      command: systemctl is-active x-ui
      register: service_status
      changed_when: false
      ignore_errors: yes

    - name: Показать статус сервиса
      debug:
        msg: "Статус x-ui: {{ service_status.stdout }}"
