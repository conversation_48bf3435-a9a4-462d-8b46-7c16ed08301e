---
- name: Настроить маршрутизацию Xray в 3x-ui панели
  hosts: webservers
  become: true
  vars:
    xray_config_path: "/usr/local/x-ui/bin/config.json"
    backup_dir: "/root/cert/xray_backups"

    # Правила маршрутизации для добавления
    routing_rules:
      - type: "field"
        domain:
          - "geosite:category-gov-ru"
        outboundTag: "direct"
        comment: "Russian government sites"
      - type: "field"
        domain:
          - "regexp:.*\\.ru$"
        outboundTag: "direct"
        comment: "All .ru domains"
      - type: "field"
        domain:
          - "geosite:openai"
        outboundTag: "direct"
        comment: "OpenAI services"
      - type: "field"
        ip:
          - "geoip:ru"
        outboundTag: "direct"
        comment: "Russian IP addresses"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Установить необходимые пакеты
      apt:
        name:
          - jq
          - python3-json
        state: present

    - name: Создать директорию для резервных копий
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: "0700"
        owner: root
        group: root

    - name: Проверить существование конфигурационного файла Xray
      stat:
        path: "{{ xray_config_path }}"
      register: xray_config_exists

    - name: Завершить выполнение если конфигурация не найдена
      fail:
        msg: "Конфигурационный файл Xray не найден: {{ xray_config_path }}"
      when: not xray_config_exists.stat.exists

    - name: Создать резервную копию конфигурации Xray
      copy:
        src: "{{ xray_config_path }}"
        dest: "{{ backup_dir }}/config.json.backup.{{ ansible_date_time.epoch }}"
        remote_src: yes
        mode: "0600"

    - name: Проверить валидность JSON конфигурации
      shell: |
        jq empty "{{ xray_config_path }}"
      register: json_validation
      failed_when: json_validation.rc != 0
      changed_when: false

    - name: Прочитать текущую конфигурацию Xray
      slurp:
        src: "{{ xray_config_path }}"
      register: current_config_raw

    - name: Парсинг текущей конфигурации
      set_fact:
        current_config: "{{ current_config_raw.content | b64decode | from_json }}"

    - name: Показать текущую структуру routing
      debug:
        msg: "Текущая конфигурация routing: {{ current_config.routing | default('Не настроена') }}"

    - name: Инициализировать routing если не существует
      set_fact:
        updated_config: "{{ current_config | combine({'routing': {'rules': []}}) }}"
      when: current_config.routing is not defined

    - name: Использовать существующую конфигурацию routing
      set_fact:
        updated_config: "{{ current_config }}"
      when: current_config.routing is defined

    - name: Инициализировать rules если не существует
      set_fact:
        updated_config: "{{ updated_config | combine({'routing': {'rules': []}}, recursive=true) }}"
      when: updated_config.routing.rules is not defined

    - name: Получить существующие правила
      set_fact:
        existing_rules: "{{ updated_config.routing.rules | default([]) }}"

    - name: Создать Python скрипт для добавления правил маршрутизации
      copy:
        content: |
          #!/usr/bin/env python3
          import json
          import sys

          def rule_exists(existing_rules, new_rule):
              """Проверить существует ли правило"""
              for rule in existing_rules:
                  # Проверяем по domain правилам
                  if 'domain' in new_rule and 'domain' in rule:
                      if set(new_rule['domain']) == set(rule['domain']):
                          return True
                  # Проверяем по IP правилам
                  if 'ip' in new_rule and 'ip' in rule:
                      if set(new_rule['ip']) == set(rule['ip']):
                          return True
              return False

          def add_routing_rules(config, new_rules):
              """Добавить новые правила маршрутизации"""
              if 'routing' not in config:
                  config['routing'] = {'rules': []}
              
              if 'rules' not in config['routing']:
                  config['routing']['rules'] = []
              
              existing_rules = config['routing']['rules']
              added_rules = []
              
              for rule in new_rules:
                  if not rule_exists(existing_rules, rule):
                      # Удаляем comment перед добавлением (не поддерживается в JSON)
                      rule_to_add = {k: v for k, v in rule.items() if k != 'comment'}
                      existing_rules.append(rule_to_add)
                      added_rules.append(rule)
                      print(f"Добавлено правило: {rule.get('comment', 'Без описания')}")
                  else:
                      print(f"Правило уже существует: {rule.get('comment', 'Без описания')}")
              
              return config, added_rules

          if __name__ == "__main__":
              config_file = sys.argv[1]
              rules_file = sys.argv[2]
              
              # Читаем конфигурацию
              with open(config_file, 'r', encoding='utf-8') as f:
                  config = json.load(f)
              
              # Читаем новые правила
              with open(rules_file, 'r', encoding='utf-8') as f:
                  new_rules = json.load(f)
              
              # Добавляем правила
              updated_config, added_rules = add_routing_rules(config, new_rules)
              
              # Сохраняем обновленную конфигурацию
              with open(config_file, 'w', encoding='utf-8') as f:
                  json.dump(updated_config, f, indent=2, ensure_ascii=False)
              
              print(f"Обработано правил: {len(new_rules)}")
              print(f"Добавлено новых правил: {len(added_rules)}")
        dest: /tmp/add_routing_rules.py
        mode: "0755"

    - name: Создать файл с новыми правилами маршрутизации
      copy:
        content: "{{ routing_rules | to_nice_json }}"
        dest: /tmp/new_routing_rules.json
        mode: "0644"

    - name: Добавить правила маршрутизации в конфигурацию Xray
      command: python3 /tmp/add_routing_rules.py "{{ xray_config_path }}" /tmp/new_routing_rules.json
      register: routing_update_result

    - name: Показать результат добавления правил
      debug:
        msg: "{{ routing_update_result.stdout_lines }}"

    - name: Проверить валидность обновленной конфигурации
      shell: |
        jq empty "{{ xray_config_path }}"
      register: updated_json_validation
      failed_when: updated_json_validation.rc != 0

    - name: Прочитать обновленную конфигурацию для проверки
      slurp:
        src: "{{ xray_config_path }}"
      register: updated_config_raw

    - name: Парсинг обновленной конфигурации
      set_fact:
        final_config: "{{ updated_config_raw.content | b64decode | from_json }}"

    - name: Показать количество правил маршрутизации
      debug:
        msg:
          - "Общее количество правил маршрутизации: {{ final_config.routing.rules | length }}"
          - "Правила с outboundTag 'direct': {{ final_config.routing.rules | selectattr('outboundTag', 'equalto', 'direct') | list | length }}"

    - name: Остановить x-ui для применения изменений
      systemd:
        name: x-ui
        state: stopped

    - name: Запустить x-ui с новой конфигурацией
      systemd:
        name: x-ui
        state: started

    - name: Подождать запуска сервиса
      pause:
        seconds: 5

    - name: Проверить статус x-ui
      systemd:
        name: x-ui
      register: service_status

    - name: Создать отчет о настройке маршрутизации
      copy:
        content: |
          ==========================================
          🛣️  НАСТРОЙКА МАРШРУТИЗАЦИИ XRAY
          ==========================================

          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          📁 Конфигурация: {{ xray_config_path }}

          ==========================================
          📋 ДОБАВЛЕННЫЕ ПРАВИЛА МАРШРУТИЗАЦИИ
          ==========================================

          1. 🏛️ Российские государственные сайты
             Домены: geosite:category-gov-ru
             Маршрут: direct (прямое подключение)

          2. 🇷🇺 Все .ru домены
             Домены: regexp:.*\.ru$ (регулярное выражение)
             Маршрут: direct (прямое подключение)

          3. 🤖 Сервисы OpenAI
             Домены: geosite:openai
             Маршрут: direct (прямое подключение)

          4. 🌐 Российские IP адреса
             IP диапазоны: geoip:ru
             Маршрут: direct (прямое подключение)

          ==========================================
          📊 СТАТИСТИКА
          ==========================================

          Общее количество правил: {{ final_config.routing.rules | length }}
          Правил с direct маршрутом: {{ final_config.routing.rules | selectattr('outboundTag', 'equalto', 'direct') | list | length }}
          Статус x-ui: {{ service_status.status.ActiveState }}

          ==========================================
          💾 РЕЗЕРВНЫЕ КОПИИ
          ==========================================

          Оригинальная конфигурация сохранена:
          {{ backup_dir }}/config.json.backup.{{ ansible_date_time.epoch }}

          Для восстановления:
          1. systemctl stop x-ui
          2. cp {{ backup_dir }}/config.json.backup.{{ ansible_date_time.epoch }} {{ xray_config_path }}
          3. systemctl start x-ui

          ==========================================
          🔧 РЕЗУЛЬТАТ ВЫПОЛНЕНИЯ
          ==========================================

          {{ routing_update_result.stdout }}

          ==========================================
          ✅ ПРОВЕРКА КОНФИГУРАЦИИ
          ==========================================

          JSON валидность: ✅ Корректная
          Сервис x-ui: {{ '✅ Запущен' if service_status.status.ActiveState == 'active' else '❌ Проблемы' }}

          ==========================================
          📖 ОПИСАНИЕ ПРАВИЛ
          ==========================================

          • geosite:category-gov-ru - Российские государственные домены
          • regexp:.*\.ru$ - Все домены с окончанием .ru
          • geosite:openai - Домены связанные с OpenAI
          • geoip:ru - IP адреса принадлежащие России

          Все эти правила настроены на прямое подключение (direct),
          что означает обход прокси для указанного трафика.

          ==========================================
        dest: "{{ backup_dir }}/ROUTING_CONFIGURATION_REPORT.txt"
        mode: "0644"

    - name: Удалить временные файлы
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /tmp/add_routing_rules.py
        - /tmp/new_routing_rules.json

    - name: Проверить работоспособность Xray после изменений
      shell: |
        timeout 10 /usr/local/x-ui/bin/xray-linux-amd64 test -config {{ xray_config_path }} 2>&1 || echo "Xray test failed or binary not found"
      register: xray_test_result
      ignore_errors: yes
      changed_when: false

    - name: Показать результат тестирования Xray
      debug:
        msg: "Результат тестирования Xray: {{ xray_test_result.stdout_lines }}"

    - name: Показать финальный результат
      debug:
        msg:
          - "🛣️ Настройка маршрутизации Xray завершена!"
          - ""
          - "📊 Статистика:"
          - "  • Всего правил: {{ final_config.routing.rules | length }}"
          - "  • Direct правил: {{ final_config.routing.rules | selectattr('outboundTag', 'equalto', 'direct') | list | length }}"
          - ""
          - "🔧 Статус x-ui: {{ service_status.status.ActiveState }}"
          - "🧪 Тест Xray: {{ 'Пройден' if 'successful' in xray_test_result.stdout else 'Проверьте вручную' }}"
          - "📁 Конфигурация: {{ xray_config_path }}"
          - "💾 Резервная копия: {{ backup_dir }}/config.json.backup.{{ ansible_date_time.epoch }}"
          - "📋 Отчет: {{ backup_dir }}/ROUTING_CONFIGURATION_REPORT.txt"
          - ""
          - "✅ Добавленные правила:"
          - "  • 🏛️ geosite:category-gov-ru → direct"
          - "  • 🇷🇺 regexp:.*\\.ru$ → direct"
          - "  • 🤖 geosite:openai → direct"
          - "  • 🌐 geoip:ru → direct"
          - ""
          - "⚠️  Если возникли проблемы, восстановите из резервной копии!"
