- name: Установить 3x-ui панель управления
  hosts: webservers
  become: true

  tasks:
    - name: Обновить пакеты и установить зависимости
      apt:
        update_cache: yes
        upgrade: dist

    - name: Установить необходимые пакеты
      apt:
        name:
          - curl
          - wget
          - unzip
        state: present

    - name: Установить 3x-ui панель управления без интерактива
      shell: |
        echo "n" | bash <(curl -Ls https://raw.githubusercontent.com/mhsanaei/3x-ui/master/install.sh)
      args:
        executable: /bin/bash
