---
- name: Быстрая диагностика 3x-ui
  hosts: webservers
  become: true
  vars:
    x_ui_db_path: "/etc/x-ui/x-ui.db"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Проверить статус сервиса x-ui
      command: systemctl is-active x-ui
      register: service_status
      changed_when: false
      ignore_errors: yes

    - name: Показать статус сервиса
      debug:
        msg: "Статус x-ui: {{ service_status.stdout }}"

    - name: Получить настройки из базы данных
      shell: |
        sqlite3 {{ x_ui_db_path }} "SELECT key, value FROM settings WHERE key LIKE 'web%' ORDER BY key;"
      register: web_settings
      changed_when: false

    - name: Показать веб-настройки
      debug:
        msg: "{{ web_settings.stdout_lines }}"

    - name: Получить пользователей
      shell: |
        sqlite3 {{ x_ui_db_path }} "SELECT id, username FROM users;"
      register: users
      changed_when: false

    - name: Показать пользователей
      debug:
        msg: "{{ users.stdout_lines }}"

    - name: Проверить дублированные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} "SELECT key, COUNT(*) FROM settings GROUP BY key HAVING COUNT(*) > 1;"
      register: duplicates
      changed_when: false

    - name: Показать дублированные настройки
      debug:
        msg: "{{ duplicates.stdout_lines if duplicates.stdout_lines else ['Дублей нет'] }}"

    - name: Проверить открытые порты
      shell: netstat -tlnp | grep x-ui || echo "x-ui не слушает порты"
      register: ports
      changed_when: false

    - name: Показать порты
      debug:
        msg: "{{ ports.stdout_lines }}"

    - name: Проверить SSL сертификаты
      stat:
        path: "{{ item }}"
      register: certs
      loop:
        - "/root/cert/cert.crt"
        - "/root/cert/secret.key"

    - name: Показать статус сертификатов
      debug:
        msg: "{{ item.item }}: {{ 'OK' if item.stat.exists else 'НЕ НАЙДЕН' }}"
      loop: "{{ certs.results }}"

    - name: Создать краткий отчет
      copy:
        content: |
          ДИАГНОСТИКА 3X-UI - {{ ansible_date_time.iso8601 }}
          ================================================
          
          IP сервера: {{ server_ip }}
          Статус x-ui: {{ service_status.stdout }}
          
          ВЕБ-НАСТРОЙКИ:
          {{ web_settings.stdout }}
          
          ПОЛЬЗОВАТЕЛИ:
          {{ users.stdout }}
          
          ДУБЛИРОВАННЫЕ НАСТРОЙКИ:
          {{ duplicates.stdout if duplicates.stdout else 'Дублей нет' }}
          
          ОТКРЫТЫЕ ПОРТЫ:
          {{ ports.stdout }}
          
          SSL СЕРТИФИКАТЫ:
          {% for item in certs.results %}
          {{ item.item }}: {{ 'OK' if item.stat.exists else 'НЕ НАЙДЕН' }}
          {% endfor %}
          
        dest: "/root/cert/quick_diagnosis.txt"
        mode: "0644"

    - name: Показать краткую сводку
      debug:
        msg:
          - "=== КРАТКАЯ ДИАГНОСТИКА ==="
          - "Статус x-ui: {{ service_status.stdout }}"
          - "Настройки в БД: {{ web_settings.stdout_lines | length }} записей"
          - "Дублированные настройки: {{ 'Есть' if duplicates.stdout_lines else 'Нет' }}"
          - "Отчет сохранен: /root/cert/quick_diagnosis.txt"
