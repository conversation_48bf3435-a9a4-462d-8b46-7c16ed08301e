---
- name: <PERSON><PERSON><PERSON><PERSON><PERSON> конфигурации <PERSON> к предыдущей версии
  hosts: webservers
  become: true
  vars:
    xray_config_path: "/usr/local/x-ui/bin/config.json"
    backup_dir: "/root/cert/xray_backups"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Проверить существование директории с резервными копиями
      stat:
        path: "{{ backup_dir }}"
      register: backup_dir_exists

    - name: Завершить если директория с резервными копиями не найдена
      fail:
        msg: "Директория с резервными копиями не найдена: {{ backup_dir }}"
      when: not backup_dir_exists.stat.exists

    - name: Найти все резервные копии конфигурации
      find:
        paths: "{{ backup_dir }}"
        patterns: "config.json.backup.*"
        file_type: file
      register: backup_files

    - name: Завершить если резервные копии не найдены
      fail:
        msg: "Резервные копии конфигурации не найдены в {{ backup_dir }}"
      when: backup_files.files | length == 0

    - name: Отсортировать резервные копии по времени создания
      set_fact:
        sorted_backups: "{{ backup_files.files | sort(attribute='mtime', reverse=true) }}"

    - name: Показать доступные резервные копии
      debug:
        msg:
          - "Найдено резервных копий: {{ sorted_backups | length }}"
          - "Последняя резервная копия: {{ sorted_backups[0].path }}"
          - "Дата создания: {{ sorted_backups[0].mtime | int | to_datetime }}"

    - name: Выбрать последнюю резервную копию для восстановления
      set_fact:
        restore_backup: "{{ sorted_backups[0].path }}"

    - name: Показать информацию о восстановлении
      debug:
        msg:
          - "🔄 Будет восстановлена конфигурация из:"
          - "📁 Файл: {{ restore_backup }}"
          - "📅 Дата: {{ sorted_backups[0].mtime | int | to_datetime }}"

    - name: Подтвердить восстановление (пауза для проверки)
      pause:
        prompt: "Нажмите Enter для продолжения восстановления или Ctrl+C для отмены"
      when: ansible_check_mode is not defined

    - name: Создать резервную копию текущей конфигурации перед откатом
      copy:
        src: "{{ xray_config_path }}"
        dest: "{{ backup_dir }}/config.json.before_rollback.{{ ansible_date_time.epoch }}"
        remote_src: yes
        mode: "0600"

    - name: Проверить валидность резервной копии
      shell: |
        jq empty "{{ restore_backup }}"
      register: backup_validation
      failed_when: backup_validation.rc != 0

    - name: Остановить x-ui перед восстановлением
      systemd:
        name: x-ui
        state: stopped

    - name: Восстановить конфигурацию из резервной копии
      copy:
        src: "{{ restore_backup }}"
        dest: "{{ xray_config_path }}"
        remote_src: yes
        mode: "0644"
        backup: yes

    - name: Проверить валидность восстановленной конфигурации
      shell: |
        jq empty "{{ xray_config_path }}"
      register: restored_validation
      failed_when: restored_validation.rc != 0

    - name: Запустить x-ui с восстановленной конфигурацией
      systemd:
        name: x-ui
        state: started

    - name: Подождать запуска сервиса
      pause:
        seconds: 5

    - name: Проверить статус x-ui после восстановления
      systemd:
        name: x-ui
      register: service_status

    - name: Прочитать восстановленную конфигурацию для анализа
      slurp:
        src: "{{ xray_config_path }}"
      register: restored_config_raw

    - name: Парсинг восстановленной конфигурации
      set_fact:
        restored_config: "{{ restored_config_raw.content | b64decode | from_json }}"

    - name: Получить статистику восстановленных правил
      set_fact:
        restored_rules: "{{ restored_config.routing.rules | default([]) }}"
        restored_direct_rules: "{{ restored_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'direct') | list }}"

    - name: Проверить работоспособность Xray после восстановления
      shell: |
        timeout 10 /usr/local/x-ui/bin/xray-linux-amd64 test -config {{ xray_config_path }} 2>&1 || echo "Xray test failed or binary not found"
      register: xray_test_result
      ignore_errors: yes
      changed_when: false

    - name: Создать отчет о восстановлении
      copy:
        content: |
          ==========================================
          🔄 ОТЧЕТ О ВОССТАНОВЛЕНИИ КОНФИГУРАЦИИ XRAY
          ==========================================
          
          📅 Дата восстановления: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          
          ==========================================
          📁 ИНФОРМАЦИЯ О ВОССТАНОВЛЕНИИ
          ==========================================
          
          Восстановлено из: {{ restore_backup }}
          Дата резервной копии: {{ sorted_backups[0].mtime | int | to_datetime }}
          Текущая конфигурация: {{ xray_config_path }}
          
          ==========================================
          💾 РЕЗЕРВНЫЕ КОПИИ
          ==========================================
          
          Конфигурация перед откатом сохранена:
          {{ backup_dir }}/config.json.before_rollback.{{ ansible_date_time.epoch }}
          
          Доступные резервные копии:
          {% for backup in sorted_backups[:5] %}
          • {{ backup.path }} ({{ backup.mtime | int | to_datetime }})
          {% endfor %}
          {% if sorted_backups | length > 5 %}
          ... и еще {{ sorted_backups | length - 5 }} файлов
          {% endif %}
          
          ==========================================
          📊 СТАТИСТИКА ВОССТАНОВЛЕННОЙ КОНФИГУРАЦИИ
          ==========================================
          
          Всего правил маршрутизации: {{ restored_rules | length }}
          Правил с direct маршрутом: {{ restored_direct_rules | length }}
          
          ==========================================
          🔧 СТАТУС СИСТЕМЫ
          ==========================================
          
          Статус x-ui: {{ service_status.status.ActiveState }}
          Тест Xray: {{ 'Пройден' if 'successful' in xray_test_result.stdout else 'Проверьте вручную' }}
          JSON валидность: ✅ Корректная
          
          ==========================================
          🧪 РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ XRAY
          ==========================================
          
          {{ xray_test_result.stdout }}
          
          ==========================================
          📋 СЛЕДУЮЩИЕ ШАГИ
          ==========================================
          
          1. Проверьте работу панели 3x-ui
          2. Убедитесь что маршрутизация работает корректно
          3. При необходимости повторно настройте правила:
             ansible-playbook -i inventory.ini configure_xray_routing.yml
          
          ==========================================
          🔄 ПОВТОРНОЕ ВОССТАНОВЛЕНИЕ
          ==========================================
          
          Для восстановления другой резервной копии:
          1. Выберите нужный файл из списка выше
          2. Скопируйте его: cp BACKUP_FILE {{ xray_config_path }}
          3. Перезапустите: systemctl restart x-ui
          
          ==========================================
        dest: "{{ backup_dir }}/ROLLBACK_REPORT.txt"
        mode: "0644"

    - name: Показать результат восстановления
      debug:
        msg:
          - "🔄 Восстановление конфигурации Xray завершено!"
          - ""
          - "📁 Восстановлено из: {{ restore_backup | basename }}"
          - "📅 Дата резервной копии: {{ sorted_backups[0].mtime | int | to_datetime }}"
          - ""
          - "📊 Статистика восстановленной конфигурации:"
          - "  • Всего правил: {{ restored_rules | length }}"
          - "  • Direct правил: {{ restored_direct_rules | length }}"
          - ""
          - "🔧 Статус x-ui: {{ service_status.status.ActiveState }}"
          - "🧪 Тест Xray: {{ 'Пройден' if 'successful' in xray_test_result.stdout else 'Проверьте вручную' }}"
          - ""
          - "💾 Резервная копия перед откатом:"
          - "   {{ backup_dir }}/config.json.before_rollback.{{ ansible_date_time.epoch }}"
          - "📋 Отчет: {{ backup_dir }}/ROLLBACK_REPORT.txt"
          - ""
          - "✅ Конфигурация успешно восстановлена!"

    - name: Показать доступные резервные копии для справки
      debug:
        msg:
          - "📁 Доступные резервные копии:"
          - "{% for backup in sorted_backups[:10] %}"
          - "  • {{ backup.path | basename }} ({{ backup.mtime | int | to_datetime }})"
          - "{% endfor %}"
          - "{% if sorted_backups | length > 10 %}"
          - "  ... и еще {{ sorted_backups | length - 10 }} файлов"
          - "{% endif %}"
