- name: Установка Docker на Ubuntu
  hosts: webservers
  become: true

  tasks:
    - name: Установить необходимые пакеты
      apt:
        name:
          - apt-transport-https
          - ca-certificates
          - curl
          - software-properties-common
        state: present
        update_cache: yes

    - name: Добавить официальный GPG ключ Docker
      apt_key:
        url: https://download.docker.com/linux/ubuntu/gpg
        state: present

    - name: Добавить репозиторий Docker
      apt_repository:
        repo: deb [arch=amd64] https://download.docker.com/linux/ubuntu focal stable
        state: present
        filename: docker

    - name: Обновить кэш пакетов
      apt:
        update_cache: yes

    - name: Установить Docker CE
      apt:
        name: docker-ce
        state: latest

    - name: Убедиться, что служба Docker запущена и включена
      service:
        name: docker
        state: started
        enabled: yes
