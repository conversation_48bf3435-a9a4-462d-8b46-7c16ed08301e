- name: Установка и настройка Fail2Ban
  hosts: webservers
  become: true

  tasks:
    - name: Установить fail2ban
      apt:
        name: fail2ban
        state: present
        update_cache: yes

    - name: Скопировать конфигурацию jail.local
      copy:
        dest: /etc/fail2ban/jail.local
        content: |
          [sshd]
          enabled = true
          port = ssh
          filter = sshd
          logpath = /var/log/auth.log
          maxretry = 5
          bantime = 600
          findtime = 600

    - name: Перезапустить fail2ban
      service:
        name: fail2ban
        state: restarted
        enabled: yes
