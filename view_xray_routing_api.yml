---
- name: Просмотр маршрутизации Xray через API 3x-ui панели
  hosts: webservers
  become: true
  vars:
    # Учетные данные для API 3x-ui
    panel_url: "http://************:23456/IxIPY9iNxOOpf9Zb6n"
    panel_username: "fSkACNeS"
    panel_password: "imGecmwscOT7Pm"
    
    # Директория для отчетов
    backup_dir: "/root/cert/xray_backups"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать директорию для отчетов
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: "0700"

    - name: Проверить доступность панели 3x-ui
      uri:
        url: "{{ panel_url }}"
        method: GET
        timeout: 10
        status_code: [200, 302, 401]
      register: panel_availability

    - name: Показать статус доступности панели
      debug:
        msg:
          - "🌐 Проверка доступности панели:"
          - "📡 URL: {{ panel_url }}"
          - "📊 HTTP статус: {{ panel_availability.status | default('Недоступна') }}"

    - name: Аутентификация в панели 3x-ui
      uri:
        url: "{{ panel_url }}/login"
        method: POST
        body_format: form-urlencoded
        body:
          username: "{{ panel_username }}"
          password: "{{ panel_password }}"
        timeout: 30
        status_code: [200, 302]
        follow_redirects: no
      register: login_response
      ignore_errors: yes

    - name: Показать результат аутентификации
      debug:
        msg:
          - "🔐 Аутентификация:"
          - "📊 HTTP статус: {{ login_response.status | default('Ошибка') }}"
          - "🍪 Cookies: {{ 'Получены' if login_response.cookies is defined else 'Отсутствуют' }}"

    - name: Получить конфигурацию Xray через API
      uri:
        url: "{{ panel_url }}/panel/api/xray/config"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200]
      register: config_response
      ignore_errors: yes
      when: login_response.status is defined and login_response.status in [200, 302]

    - name: Показать результат получения конфигурации
      debug:
        msg:
          - "📋 Получение конфигурации через API:"
          - "📊 HTTP статус: {{ config_response.status | default('Ошибка') }}"
          - "📏 Размер ответа: {{ config_response.content | default('') | length }} символов"

    - name: Парсинг конфигурации Xray
      set_fact:
        xray_config: "{{ config_response.json | default({}) }}"
      when: config_response.status is defined and config_response.status == 200

    - name: Анализ правил маршрутизации
      set_fact:
        routing_rules: "{{ xray_config.routing.rules | default([]) }}"
        direct_rules: "{{ xray_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'direct') | list }}"
        blocked_rules: "{{ xray_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'blocked') | list }}"
        api_rules: "{{ xray_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'api') | list }}"
        domain_rules: "{{ xray_config.routing.rules | default([]) | selectattr('domain', 'defined') | list }}"
        ip_rules: "{{ xray_config.routing.rules | default([]) | selectattr('ip', 'defined') | list }}"
      when: xray_config is defined

    - name: Проверить наличие наших специфических правил
      set_fact:
        our_rules_check:
          gov_ru: "{{ routing_rules | selectattr('domain', 'defined') | selectattr('domain', 'contains', 'geosite:category-gov-ru') | list | length > 0 }}"
          ru_domains: "{{ routing_rules | selectattr('domain', 'defined') | selectattr('domain', 'contains', 'regexp:.*\\.ru$') | list | length > 0 }}"
          openai: "{{ routing_rules | selectattr('domain', 'defined') | selectattr('domain', 'contains', 'geosite:openai') | list | length > 0 }}"
          ru_ip: "{{ routing_rules | selectattr('ip', 'defined') | selectattr('ip', 'contains', 'geoip:ru') | list | length > 0 }}"
      when: routing_rules is defined

    - name: Показать общую статистику
      debug:
        msg:
          - "📊 СТАТИСТИКА МАРШРУТИЗАЦИИ (через API):"
          - "📋 Всего правил: {{ routing_rules | default([]) | length }}"
          - "🎯 Direct правил: {{ direct_rules | default([]) | length }}"
          - "🚫 Blocked правил: {{ blocked_rules | default([]) | length }}"
          - "🔧 API правил: {{ api_rules | default([]) | length }}"
          - "🌐 Правил по доменам: {{ domain_rules | default([]) | length }}"
          - "📍 Правил по IP: {{ ip_rules | default([]) | length }}"
      when: routing_rules is defined

    - name: Показать проверку наших правил
      debug:
        msg:
          - "✅ ПРОВЕРКА НАШИХ ПРАВИЛ:"
          - "🏛️ geosite:category-gov-ru: {{ '✅ Настроено' if our_rules_check.gov_ru else '❌ Отсутствует' }}"
          - "🇷🇺 regexp:.*\\.ru$: {{ '✅ Настроено' if our_rules_check.ru_domains else '❌ Отсутствует' }}"
          - "🤖 geosite:openai: {{ '✅ Настроено' if our_rules_check.openai else '❌ Отсутствует' }}"
          - "🌐 geoip:ru: {{ '✅ Настроено' if our_rules_check.ru_ip else '❌ Отсутствует' }}"
      when: our_rules_check is defined

    - name: Показать детальную информацию о direct правилах
      debug:
        msg:
          - "🎯 ДЕТАЛЬНАЯ ИНФОРМАЦИЯ О DIRECT ПРАВИЛАХ:"
          - "{% for rule in direct_rules | default([]) %}"
          - "  [{{ loop.index0 }}] {{ rule.type | default('field') }}"
          - "    {% if rule.domain is defined %}Домены: {{ rule.domain | join(', ') }}{% endif %}"
          - "    {% if rule.ip is defined %}IP: {{ rule.ip | join(', ') }}{% endif %}"
          - "    {% if rule.port is defined %}Порты: {{ rule.port }}{% endif %}"
          - "    {% if rule.protocol is defined %}Протоколы: {{ rule.protocol | join(', ') }}{% endif %}"
          - "{% endfor %}"
      when: direct_rules is defined and direct_rules | length > 0

    - name: Получить информацию о inbound подключениях
      uri:
        url: "{{ panel_url }}/panel/api/inbounds/list"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200]
      register: inbounds_response
      ignore_errors: yes
      when: login_response.status is defined and login_response.status in [200, 302]

    - name: Показать информацию о inbound подключениях
      debug:
        msg:
          - "📡 INBOUND ПОДКЛЮЧЕНИЯ:"
          - "📊 HTTP статус: {{ inbounds_response.status | default('Ошибка') }}"
          - "📋 Количество: {{ inbounds_response.json | default([]) | length if inbounds_response.status == 200 else 'N/A' }}"
      when: inbounds_response is defined

    - name: Создать отчет о просмотре через API
      copy:
        content: |
          ==========================================
          📊 ОТЧЕТ О МАРШРУТИЗАЦИИ ЧЕРЕЗ API 3X-UI
          ==========================================
          
          📅 Дата анализа: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          📡 URL панели: {{ panel_url }}
          
          ==========================================
          🔐 ПОДКЛЮЧЕНИЕ К API
          ==========================================
          
          Доступность панели: {{ '✅ Доступна' if panel_availability.status in [200, 302, 401] else '❌ Недоступна' }}
          Аутентификация: {{ '✅ Успешно' if login_response.status in [200, 302] else '❌ Ошибка' }}
          Получение конфигурации: {{ '✅ Успешно' if config_response.status == 200 else '❌ Ошибка' }}
          
          ==========================================
          📊 ОБЩАЯ СТАТИСТИКА
          ==========================================
          
          {% if routing_rules is defined %}
          Всего правил маршрутизации: {{ routing_rules | length }}
          Direct правил: {{ direct_rules | length }}
          Blocked правил: {{ blocked_rules | length }}
          API правил: {{ api_rules | length }}
          Правил по доменам: {{ domain_rules | length }}
          Правил по IP: {{ ip_rules | length }}
          {% else %}
          Статистика недоступна (ошибка получения конфигурации)
          {% endif %}
          
          ==========================================
          ✅ ПРОВЕРКА НАШИХ ПРАВИЛ
          ==========================================
          
          {% if our_rules_check is defined %}
          🏛️ geosite:category-gov-ru: {{ '✅ Настроено' if our_rules_check.gov_ru else '❌ Отсутствует' }}
          🇷🇺 regexp:.*\.ru$: {{ '✅ Настроено' if our_rules_check.ru_domains else '❌ Отсутствует' }}
          🤖 geosite:openai: {{ '✅ Настроено' if our_rules_check.openai else '❌ Отсутствует' }}
          🌐 geoip:ru: {{ '✅ Настроено' if our_rules_check.ru_ip else '❌ Отсутствует' }}
          {% else %}
          Проверка правил недоступна
          {% endif %}
          
          ==========================================
          🎯 DIRECT ПРАВИЛА (ДЕТАЛЬНО)
          ==========================================
          
          {% if direct_rules is defined %}
          {% for rule in direct_rules %}
          [{{ loop.index0 }}] {{ rule.type | default('field') }}
          {% if rule.domain is defined %}  Домены: {{ rule.domain | join(', ') }}{% endif %}
          {% if rule.ip is defined %}  IP: {{ rule.ip | join(', ') }}{% endif %}
          {% if rule.port is defined %}  Порты: {{ rule.port }}{% endif %}
          {% if rule.protocol is defined %}  Протоколы: {{ rule.protocol | join(', ') }}{% endif %}
          
          {% endfor %}
          {% else %}
          Direct правила недоступны
          {% endif %}
          
          ==========================================
          📡 INBOUND ПОДКЛЮЧЕНИЯ
          ==========================================
          
          {% if inbounds_response.status == 200 %}
          Количество inbound подключений: {{ inbounds_response.json | length }}
          {% else %}
          Информация о inbound подключениях недоступна
          {% endif %}
          
          ==========================================
          🔧 УПРАВЛЕНИЕ ЧЕРЕЗ API
          ==========================================
          
          Для добавления правил через API:
          ansible-playbook -i inventory.ini configure_xray_routing_api.yml
          
          Для просмотра через API:
          ansible-playbook -i inventory.ini view_xray_routing_api.yml
          
          ==========================================
          💡 ПРЕИМУЩЕСТВА API ПОДХОДА
          ==========================================
          
          • Использует официальный интерфейс 3x-ui
          • Получает актуальную конфигурацию в реальном времени
          • Избегает проблем с кешированием файлов
          • Поддерживает аутентификацию и авторизацию
          • Интегрируется с системой управления панели
          
          ==========================================
        dest: "{{ backup_dir }}/API_VIEW_REPORT.txt"
        mode: "0644"

    - name: Показать краткую сводку
      debug:
        msg:
          - "📊 Просмотр маршрутизации через API завершен"
          - ""
          - "🔐 Подключение:"
          - "  • Панель: {{ '✅ Доступна' if panel_availability.status in [200, 302, 401] else '❌ Недоступна' }}"
          - "  • Аутентификация: {{ '✅ Успешно' if login_response.status in [200, 302] else '❌ Ошибка' }}"
          - "  • API конфигурация: {{ '✅ Получена' if config_response.status == 200 else '❌ Ошибка' }}"
          - ""
          - "📊 Статистика:"
          - "  {% if routing_rules is defined %}• Всего правил: {{ routing_rules | length }}{% endif %}"
          - "  {% if direct_rules is defined %}• Direct правил: {{ direct_rules | length }}{% endif %}"
          - ""
          - "✅ Наши правила:"
          - "  {% if our_rules_check is defined %}• geosite:category-gov-ru: {{ '✅' if our_rules_check.gov_ru else '❌' }}{% endif %}"
          - "  {% if our_rules_check is defined %}• regexp:.*\\.ru$: {{ '✅' if our_rules_check.ru_domains else '❌' }}{% endif %}"
          - "  {% if our_rules_check is defined %}• geosite:openai: {{ '✅' if our_rules_check.openai else '❌' }}{% endif %}"
          - "  {% if our_rules_check is defined %}• geoip:ru: {{ '✅' if our_rules_check.ru_ip else '❌' }}{% endif %}"
          - ""
          - "📋 Отчет: {{ backup_dir }}/API_VIEW_REPORT.txt"
          - "📡 Панель: {{ panel_url }}"
