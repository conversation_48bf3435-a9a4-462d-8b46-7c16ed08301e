---
- name: Создать SSL сертификаты для 3x-ui панели (ручной метод)
  hosts: webservers
  become: true
  vars:
    cert_dir: "/root/cert"
    cert_validity_days: 365

  tasks:
    - name: Установить OpenSSL (если не установлен)
      apt:
        name: openssl
        state: present
        update_cache: yes

    - name: Создать директорию для сертификатов
      file:
        path: "{{ cert_dir }}"
        state: directory
        mode: '0700'
        owner: root
        group: root

    - name: <PERSON>о<PERSON><PERSON><PERSON><PERSON><PERSON>ь IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать закрытый ключ (2048 бит)
      command: openssl genrsa -out secret.key 2048
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/secret.key"

    - name: Установить права на закрытый ключ
      file:
        path: "{{ cert_dir }}/secret.key"
        mode: '0600'
        owner: root
        group: root

    - name: Создать конфигурационный файл для CSR
      copy:
        content: |
          [req]
          distinguished_name = req_distinguished_name
          req_extensions = v3_req
          prompt = no

          [req_distinguished_name]
          C = RU
          ST = Moscow
          L = Moscow
          O = Private
          OU = IT
          CN = {{ server_ip }}
          emailAddress = admin@{{ server_ip }}

          [v3_req]
          keyUsage = keyEncipherment, dataEncipherment
          extendedKeyUsage = serverAuth
          subjectAltName = @alt_names

          [alt_names]
          IP.1 = {{ server_ip }}
        dest: "{{ cert_dir }}/openssl.conf"
        mode: '0644'

    - name: Создать запрос на подпись сертификата (CSR)
      command: openssl req -key secret.key -new -out cert.csr -config openssl.conf
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/cert.csr"

    - name: Создать самоподписанный сертификат
      command: openssl x509 -signkey secret.key -in cert.csr -req -days {{ cert_validity_days }} -out cert.crt -extensions v3_req -extfile openssl.conf
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/cert.crt"

    - name: Установить права на сертификат
      file:
        path: "{{ cert_dir }}/cert.crt"
        mode: '0644'
        owner: root
        group: root

    - name: Получить текущий путь к директории
      command: pwd
      args:
        chdir: "{{ cert_dir }}"
      register: cert_path
      changed_when: false

    - name: Проверить созданные файлы
      stat:
        path: "{{ cert_dir }}/{{ item }}"
      register: cert_files_check
      loop:
        - secret.key
        - cert.csr
        - cert.crt

    - name: Показать статус файлов
      debug:
        msg: "Файл {{ item.item }} существует: {{ item.stat.exists }}"
      loop: "{{ cert_files_check.results }}"

    - name: Показать информацию о сертификате
      command: openssl x509 -in cert.crt -text -noout
      args:
        chdir: "{{ cert_dir }}"
      register: cert_details
      changed_when: false

    - name: Создать файл с инструкциями
      copy:
        content: |
          ===========================================
          SSL СЕРТИФИКАТЫ СОЗДАНЫ УСПЕШНО
          ===========================================
          
          Директория с сертификатами: {{ cert_path.stdout }}
          
          Пути к файлам для настройки 3x-ui панели:
          
          Путь к файлу публичного ключа сертификата панели:
          {{ cert_path.stdout }}/cert.crt
          
          Путь к файлу приватного ключа сертификата панели:
          {{ cert_path.stdout }}/secret.key
          
          IP адрес сервера (Common Name): {{ server_ip }}
          Срок действия сертификата: {{ cert_validity_days }} дней
          
          ===========================================
          ИНСТРУКЦИЯ ПО НАСТРОЙКЕ 3X-UI:
          ===========================================
          
          1. Откройте веб-интерфейс 3x-ui панели
          2. Перейдите в настройки панели
          3. В поле "Путь к файлу публичного ключа" укажите:
             {{ cert_path.stdout }}/cert.crt
          4. В поле "Путь к файлу приватного ключа" укажите:
             {{ cert_path.stdout }}/secret.key
          5. Сохраните настройки
          6. Также добавьте эти ключи во вкладку "Подписка"
          7. Перезапустите панель для применения изменений
          
          ===========================================
        dest: "{{ cert_dir }}/ИНСТРУКЦИЯ.txt"
        mode: '0644'

    - name: Показать финальную информацию
      debug:
        msg:
          - "✅ SSL сертификаты успешно созданы!"
          - "📁 Директория: {{ cert_path.stdout }}"
          - "🔐 Публичный ключ: {{ cert_path.stdout }}/cert.crt"
          - "🔑 Приватный ключ: {{ cert_path.stdout }}/secret.key"
          - "🌐 IP адрес: {{ server_ip }}"
          - "📋 Инструкция сохранена в: {{ cert_path.stdout }}/ИНСТРУКЦИЯ.txt"

    - name: Показать детали сертификата
      debug:
        var: cert_details.stdout_lines
