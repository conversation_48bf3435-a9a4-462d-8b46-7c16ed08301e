---
- name: Тестирование установки и настройки Playwright
  hosts: webservers
  become: true
  vars:
    panel_url: "http://************:23456/IxIPY9iNxOOpf9Zb6n"
    panel_username: "fSkACNeS"
    panel_password: "imGecmwscOT7Pm"
    backup_dir: "/root/cert/xray_backups"
    screenshots_dir: "{{ backup_dir }}/test_screenshots"

  tasks:
    - name: Создать директории для тестирования
      file:
        path: "{{ item }}"
        state: directory
        mode: "0755"
      loop:
        - "{{ backup_dir }}"
        - "{{ screenshots_dir }}"

    - name: Установить системные зависимости для Playwright
      apt:
        name:
          - python3
          - python3-pip
          - python3-venv
          - libnss3
          - libatk-bridge2.0-0
          - libdrm2
          - libxkbcommon0
          - libxcomposite1
          - libxdamage1
          - libxrandr2
          - libgbm1
          - libxss1
          - libasound2t64
          - libatspi2.0-0
          - libgtk-3-0
          - libgdk-pixbuf2.0-0
          - xvfb
          - fonts-liberation
          - fonts-dejavu-core
          - ca-certificates
        state: present
        update_cache: yes

    - name: Создать виртуальное окружение Python
      command: python3 -m venv /opt/playwright-env
      args:
        creates: /opt/playwright-env

    - name: Обновить pip в виртуальном окружении
      pip:
        name: pip
        state: latest
        virtualenv: /opt/playwright-env

    - name: Установить Playwright и зависимости
      pip:
        name:
          - playwright==1.40.0
          - asyncio
        virtualenv: /opt/playwright-env

    - name: Установить браузеры Playwright
      shell: |
        /opt/playwright-env/bin/python -m playwright install chromium
      environment:
        PLAYWRIGHT_BROWSERS_PATH: /opt/playwright-env/browsers
      register: browser_install
      changed_when: "'chromium' in browser_install.stdout"

    - name: Показать результат установки браузера
      debug:
        msg: "{{ browser_install.stdout_lines }}"

    - name: Создать простой тестовый скрипт Playwright
      copy:
        content: |
          #!/usr/bin/env python3
          import asyncio
          import sys
          import os
          from datetime import datetime
          from playwright.async_api import async_playwright

          async def test_playwright():
              print(f"[{datetime.now()}] Starting Playwright test...")
              
              try:
                  playwright = await async_playwright().start()
                  print(f"[{datetime.now()}] Playwright started successfully")
                  
                  browser = await playwright.chromium.launch(
                      headless=True,
                      args=[
                          '--no-sandbox',
                          '--disable-setuid-sandbox',
                          '--disable-dev-shm-usage',
                          '--disable-gpu'
                      ]
                  )
                  print(f"[{datetime.now()}] Browser launched successfully")
                  
                  context = await browser.new_context(
                      viewport={'width': 1920, 'height': 1080}
                  )
                  
                  page = await context.new_page()
                  print(f"[{datetime.now()}] Page created successfully")
                  
                  # Тест 1: Переход на панель
                  panel_url = sys.argv[1]
                  print(f"[{datetime.now()}] Navigating to: {panel_url}")
                  
                  await page.goto(panel_url, wait_until='networkidle', timeout=30000)
                  print(f"[{datetime.now()}] Page loaded successfully")
                  
                  # Сделать скриншот
                  screenshot_path = os.path.join(sys.argv[2], f"test_page_{datetime.now().strftime('%H%M%S')}.png")
                  await page.screenshot(path=screenshot_path, full_page=True)
                  print(f"[{datetime.now()}] Screenshot saved: {screenshot_path}")
                  
                  # Получить заголовок страницы
                  title = await page.title()
                  print(f"[{datetime.now()}] Page title: {title}")
                  
                  # Получить URL
                  current_url = page.url
                  print(f"[{datetime.now()}] Current URL: {current_url}")
                  
                  # Поиск элементов формы входа
                  username_fields = await page.query_selector_all('input[type="text"], input[name*="user"], input[name*="login"]')
                  password_fields = await page.query_selector_all('input[type="password"]')
                  
                  print(f"[{datetime.now()}] Found {len(username_fields)} username fields")
                  print(f"[{datetime.now()}] Found {len(password_fields)} password fields")
                  
                  # Тест заполнения полей (если найдены)
                  if username_fields and password_fields:
                      print(f"[{datetime.now()}] Testing form filling...")
                      
                      await username_fields[0].fill(sys.argv[3])
                      await password_fields[0].fill(sys.argv[4])
                      
                      # Скриншот с заполненными полями
                      screenshot_path2 = os.path.join(sys.argv[2], f"test_filled_{datetime.now().strftime('%H%M%S')}.png")
                      await page.screenshot(path=screenshot_path2, full_page=True)
                      print(f"[{datetime.now()}] Form filled screenshot: {screenshot_path2}")
                  
                  await browser.close()
                  print(f"[{datetime.now()}] Browser closed successfully")
                  
                  print(f"[{datetime.now()}] Test completed successfully!")
                  return True
                  
              except Exception as e:
                  print(f"[{datetime.now()}] Error: {str(e)}")
                  return False

          async def main():
              if len(sys.argv) != 5:
                  print("Usage: script.py <panel_url> <screenshots_dir> <username> <password>")
                  sys.exit(1)
              
              success = await test_playwright()
              sys.exit(0 if success else 1)

          if __name__ == "__main__":
              asyncio.run(main())
        dest: /tmp/test_playwright.py
        mode: "0755"

    - name: Запустить тест Playwright
      shell: |
        export DISPLAY=:99
        Xvfb :99 -screen 0 1920x1080x24 > /dev/null 2>&1 &
        XVFB_PID=$!
        sleep 3

        export PLAYWRIGHT_BROWSERS_PATH=/opt/playwright-env/browsers
        export PYTHONUNBUFFERED=1

        timeout 60 /opt/playwright-env/bin/python /tmp/test_playwright.py \
          "{{ panel_url }}" \
          "{{ screenshots_dir }}" \
          "{{ panel_username }}" \
          "{{ panel_password }}" 2>&1

        RESULT=$?
        kill $XVFB_PID 2>/dev/null || true
        exit $RESULT
      register: test_result
      ignore_errors: yes
      args:
        executable: /bin/bash

    - name: Показать результат теста
      debug:
        msg:
          - "🧪 Результат тестирования Playwright:"
          - "📊 Код возврата: {{ test_result.rc }}"
          - "📋 Вывод:"
          - "{{ test_result.stdout_lines }}"
          - "{% if test_result.stderr_lines %}❌ Ошибки: {{ test_result.stderr_lines }}{% endif %}"

    - name: Проверить созданные тестовые скриншоты
      find:
        paths: "{{ screenshots_dir }}"
        patterns: "*.png"
      register: test_screenshots

    - name: Показать информацию о тестовых скриншотах
      debug:
        msg:
          - "📸 Создано тестовых скриншотов: {{ test_screenshots.files | length }}"
          - "📁 Директория: {{ screenshots_dir }}"
          - "{% for screenshot in test_screenshots.files %}"
          - "  • {{ screenshot.path | basename }} ({{ screenshot.size }} байт)"
          - "{% endfor %}"

    - name: Проверить версии установленных компонентов
      shell: |
        echo "=== ВЕРСИИ КОМПОНЕНТОВ ==="
        source /opt/playwright-env/bin/activate
        echo "Python: $(python3 --version)"
        echo "Playwright: $(python3 -c 'import playwright; print(playwright.__version__)')"
        echo "Chromium path: $(find /opt/playwright-env/browsers -name 'chromium*' -type d | head -1)"
        echo "Browser files: $(find /opt/playwright-env/browsers -name 'chrome' -type f | wc -l)"
      register: versions_info
      changed_when: false

    - name: Показать информацию о версиях
      debug:
        msg: "{{ versions_info.stdout_lines }}"

    - name: Создать отчет о тестировании
      copy:
        content: |
          ==========================================
          🧪 ОТЧЕТ О ТЕСТИРОВАНИИ PLAYWRIGHT
          ==========================================

          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 Тестируемый URL: {{ panel_url }}

          ==========================================
          📊 РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ
          ==========================================

          Статус: {{ 'Успешно' if test_result.rc == 0 else 'Ошибка' }}
          Код возврата: {{ test_result.rc }}
          Создано скриншотов: {{ test_screenshots.files | length }}

          ==========================================
          📋 ВЫВОД ТЕСТА
          ==========================================

          {{ test_result.stdout | default('Нет вывода') }}

          {% if test_result.stderr %}
          ==========================================
          ❌ ОШИБКИ
          ==========================================

          {{ test_result.stderr }}
          {% endif %}

          ==========================================
          📸 ТЕСТОВЫЕ СКРИНШОТЫ
          ==========================================

          {% for screenshot in test_screenshots.files %}
          {{ screenshot.path | basename }} ({{ screenshot.size }} байт)
          {% endfor %}

          ==========================================
          🔧 ВЕРСИИ КОМПОНЕНТОВ
          ==========================================

          {{ versions_info.stdout }}

          ==========================================
          💡 СЛЕДУЮЩИЕ ШАГИ
          ==========================================

          {% if test_result.rc == 0 %}
          ✅ Playwright настроен корректно!
          Можно запускать полную автоматизацию:
          ansible-playbook -i inventory.ini configure_xray_playwright.yml
          {% else %}
          ❌ Требуется устранение ошибок перед использованием
          Проверьте логи выше для диагностики проблем
          {% endif %}

          ==========================================
        dest: "{{ backup_dir }}/PLAYWRIGHT_TEST_REPORT.txt"
        mode: "0644"

    - name: Удалить тестовый скрипт
      file:
        path: /tmp/test_playwright.py
        state: absent

    - name: Показать заключение теста
      debug:
        msg:
          - "🧪 Тестирование Playwright завершено!"
          - ""
          - "📊 Результат: {{ 'Успешно' if test_result.rc == 0 else 'Ошибка' }}"
          - "📸 Скриншотов: {{ test_screenshots.files | length }}"
          - "📁 Директория: {{ screenshots_dir }}"
          - ""
          - "{% if test_result.rc == 0 %}"
          - "✅ Playwright готов к использованию!"
          - "💡 Запустите: ansible-playbook -i inventory.ini configure_xray_playwright.yml"
          - "{% else %}"
          - "❌ Требуется устранение ошибок"
          - "📋 Проверьте отчет: {{ backup_dir }}/PLAYWRIGHT_TEST_REPORT.txt"
          - "{% endif %}"
