---
- name: Исправить и проверить настройки 3x-ui базы данных
  hosts: webservers
  become: true
  vars:
    # Настройки панели
    panel_port: 8443
    panel_username: "secure_admin"
    panel_password: "MySecurePass123!"
    
    # Пути к сертификатам
    cert_dir: "/root/cert"
    ssl_cert_file: "{{ cert_dir }}/cert.crt"
    ssl_key_file: "{{ cert_dir }}/secret.key"
    
    # База данных
    x_ui_db_path: "/etc/x-ui/x-ui.db"

  tasks:
    - name: Установить sqlite3 если не установлен
      apt:
        name: sqlite3
        state: present

    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: О<PERSON><PERSON><PERSON><PERSON><PERSON>и<PERSON>ь сервис x-ui
      systemd:
        name: x-ui
        state: stopped

    - name: Создать резервную копию базы данных
      copy:
        src: "{{ x_ui_db_path }}"
        dest: "{{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}"
        remote_src: yes

    - name: Проверить текущие настройки в базе данных
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .headers on
        .mode column
        SELECT * FROM settings;
        EOF
      register: current_settings

    - name: Показать текущие настройки
      debug:
        msg: "Текущие настройки в БД:"
        var: current_settings.stdout_lines

    - name: Проверить пользователей
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .headers on
        .mode column
        SELECT id, username, password FROM users;
        EOF
      register: current_users

    - name: Показать текущих пользователей
      debug:
        msg: "Текущие пользователи:"
        var: current_users.stdout_lines

    - name: Очистить дублированные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        -- Удалить дублированные записи, оставив только последние
        DELETE FROM settings WHERE id NOT IN (
          SELECT MAX(id) FROM settings GROUP BY key
        );
        EOF

    - name: Получить MD5 хеш пароля
      shell: |
        echo -n "{{ panel_password }}" | md5sum | cut -d' ' -f1
      register: password_hash

    - name: Обновить настройки панели (правильный способ)
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        -- Удалить старые настройки
        DELETE FROM settings WHERE key IN ('webPort', 'webCertFile', 'webKeyFile', 'webListen', 'webBasePath');
        
        -- Вставить новые настройки
        INSERT INTO settings (key, value) VALUES ('webPort', '{{ panel_port }}');
        INSERT INTO settings (key, value) VALUES ('webCertFile', '{{ ssl_cert_file }}');
        INSERT INTO settings (key, value) VALUES ('webKeyFile', '{{ ssl_key_file }}');
        INSERT INTO settings (key, value) VALUES ('webListen', '');
        INSERT INTO settings (key, value) VALUES ('webBasePath', '/');
        
        -- Обновить пользователя admin
        UPDATE users SET username = '{{ panel_username }}', password = '{{ password_hash.stdout }}' WHERE id = 1;
        EOF

    - name: Проверить обновленные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .headers on
        .mode column
        SELECT * FROM settings WHERE key IN ('webPort', 'webCertFile', 'webKeyFile', 'webListen', 'webBasePath');
        EOF
      register: updated_settings

    - name: Показать обновленные настройки
      debug:
        msg: "Обновленные настройки:"
        var: updated_settings.stdout_lines

    - name: Проверить обновленного пользователя
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        .headers on
        .mode column
        SELECT id, username, password FROM users WHERE id = 1;
        EOF
      register: updated_user

    - name: Показать обновленного пользователя
      debug:
        msg: "Обновленный пользователь:"
        var: updated_user.stdout_lines

    - name: Проверить целостность базы данных
      shell: sqlite3 {{ x_ui_db_path }} "PRAGMA integrity_check;"
      register: db_integrity

    - name: Показать результат проверки целостности
      debug:
        msg: "Целостность БД: {{ db_integrity.stdout }}"

    - name: Запустить сервис x-ui
      systemd:
        name: x-ui
        state: started
        enabled: yes

    - name: Подождать запуска панели
      wait_for:
        port: "{{ panel_port }}"
        host: "0.0.0.0"
        delay: 5
        timeout: 30

    - name: Проверить доступность панели
      uri:
        url: "https://{{ server_ip }}:{{ panel_port }}"
        method: GET
        validate_certs: no
        timeout: 10
      register: panel_check
      ignore_errors: yes

    - name: Создать отчет о исправлении
      copy:
        content: |
          ==========================================
          🔧 ИСПРАВЛЕНИЕ 3X-UI БАЗЫ ДАННЫХ
          ==========================================
          
          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 IP: {{ server_ip }}
          
          ==========================================
          ✅ НОВЫЕ НАСТРОЙКИ
          ==========================================
          
          🔗 URL панели: https://{{ server_ip }}:{{ panel_port }}
          👤 Логин: {{ panel_username }}
          🔑 Пароль: {{ panel_password }}
          🔧 Порт: {{ panel_port }}
          
          ==========================================
          🔐 SSL СЕРТИФИКАТЫ
          ==========================================
          
          📁 Директория: {{ cert_dir }}
          🔐 Сертификат: {{ ssl_cert_file }}
          🔑 Ключ: {{ ssl_key_file }}
          
          ==========================================
          📋 ТЕХНИЧЕСКАЯ ИНФОРМАЦИЯ
          ==========================================
          
          💾 База данных: {{ x_ui_db_path }}
          💾 Резервная копия: {{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}
          🔒 MD5 хеш пароля: {{ password_hash.stdout }}
          ✅ Целостность БД: {{ db_integrity.stdout }}
          🌐 Статус панели: {{ 'Доступна' if panel_check.status == 200 else 'Недоступна' }}
          
          ==========================================
          📊 НАСТРОЙКИ В БАЗЕ ДАННЫХ
          ==========================================
          
          {{ updated_settings.stdout }}
          
          ==========================================
          👥 ПОЛЬЗОВАТЕЛИ
          ==========================================
          
          {{ updated_user.stdout }}
          
          ==========================================
          🚀 СЛЕДУЮЩИЕ ШАГИ
          ==========================================
          
          1. Откройте: https://{{ server_ip }}:{{ panel_port }}
          2. Войдите с новыми учетными данными
          3. Проверьте настройки SSL в панели
          4. Настройте фаервол: ufw allow {{ panel_port }}/tcp
          
          ==========================================
        dest: "{{ cert_dir }}/DATABASE_FIX_REPORT.txt"
        mode: "0600"

    - name: Показать финальный результат
      debug:
        msg:
          - "🔧 Исправление базы данных завершено!"
          - ""
          - "🔗 Панель: https://{{ server_ip }}:{{ panel_port }}"
          - "👤 Логин: {{ panel_username }}"
          - "🔑 Пароль: {{ panel_password }}"
          - ""
          - "✅ Целостность БД: {{ db_integrity.stdout }}"
          - "🌐 Панель {{ 'доступна' if panel_check.status == 200 else 'недоступна (проверьте фаервол)' }}"
          - ""
          - "📋 Отчет: {{ cert_dir }}/DATABASE_FIX_REPORT.txt"
