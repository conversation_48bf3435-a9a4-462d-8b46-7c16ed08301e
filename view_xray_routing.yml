---
- name: Просмотр и управление правилами маршрутизации Xray
  hosts: webservers
  become: true
  vars:
    xray_config_path: "/usr/local/x-ui/bin/config.json"
    backup_dir: "/root/cert/xray_backups"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Установить jq если не установлен
      apt:
        name: jq
        state: present

    - name: Проверить существование конфигурационного файла Xray
      stat:
        path: "{{ xray_config_path }}"
      register: xray_config_exists

    - name: Завершить если конфигурация не найдена
      fail:
        msg: "Конфигурационный файл Xray не найден: {{ xray_config_path }}"
      when: not xray_config_exists.stat.exists

    - name: Прочитать конфигурацию Xray
      slurp:
        src: "{{ xray_config_path }}"
      register: config_raw

    - name: Парсинг конфигурации
      set_fact:
        xray_config: "{{ config_raw.content | b64decode | from_json }}"

    - name: Получить правила маршрутизации
      set_fact:
        routing_rules: "{{ xray_config.routing.rules | default([]) }}"

    - name: Анализировать правила по типам
      set_fact:
        direct_rules: "{{ routing_rules | selectattr('outboundTag', 'equalto', 'direct') | list }}"
        proxy_rules: "{{ routing_rules | rejectattr('outboundTag', 'equalto', 'direct') | list }}"
        domain_rules: "{{ routing_rules | selectattr('domain', 'defined') | list }}"
        ip_rules: "{{ routing_rules | selectattr('ip', 'defined') | list }}"

    - name: Создать Python скрипт для детального анализа правил
      copy:
        content: |
          #!/usr/bin/env python3
          import json
          import sys
          
          def analyze_routing_rules(config):
              """Детальный анализ правил маршрутизации"""
              if 'routing' not in config or 'rules' not in config['routing']:
                  return {
                      'total_rules': 0,
                      'rules_by_tag': {},
                      'domain_rules': [],
                      'ip_rules': [],
                      'analysis': "Правила маршрутизации не настроены"
                  }
              
              rules = config['routing']['rules']
              analysis = {
                  'total_rules': len(rules),
                  'rules_by_tag': {},
                  'domain_rules': [],
                  'ip_rules': [],
                  'detailed_rules': []
              }
              
              for i, rule in enumerate(rules):
                  rule_info = {
                      'index': i,
                      'type': rule.get('type', 'field'),
                      'outboundTag': rule.get('outboundTag', 'default'),
                      'domains': rule.get('domain', []),
                      'ips': rule.get('ip', []),
                      'ports': rule.get('port', []),
                      'protocols': rule.get('protocol', [])
                  }
                  
                  analysis['detailed_rules'].append(rule_info)
                  
                  # Подсчет по тегам
                  tag = rule.get('outboundTag', 'default')
                  if tag not in analysis['rules_by_tag']:
                      analysis['rules_by_tag'][tag] = 0
                  analysis['rules_by_tag'][tag] += 1
                  
                  # Правила по доменам
                  if 'domain' in rule:
                      analysis['domain_rules'].append({
                          'index': i,
                          'domains': rule['domain'],
                          'tag': tag
                      })
                  
                  # Правила по IP
                  if 'ip' in rule:
                      analysis['ip_rules'].append({
                          'index': i,
                          'ips': rule['ip'],
                          'tag': tag
                      })
              
              return analysis
          
          if __name__ == "__main__":
              config_file = sys.argv[1]
              
              with open(config_file, 'r', encoding='utf-8') as f:
                  config = json.load(f)
              
              analysis = analyze_routing_rules(config)
              
              print("=" * 50)
              print("АНАЛИЗ ПРАВИЛ МАРШРУТИЗАЦИИ XRAY")
              print("=" * 50)
              print(f"Общее количество правил: {analysis['total_rules']}")
              print()
              
              if analysis['rules_by_tag']:
                  print("ПРАВИЛА ПО ТЕГАМ:")
                  for tag, count in analysis['rules_by_tag'].items():
                      print(f"  • {tag}: {count} правил")
                  print()
              
              if analysis['domain_rules']:
                  print("ПРАВИЛА ПО ДОМЕНАМ:")
                  for rule in analysis['domain_rules']:
                      print(f"  [{rule['index']}] → {rule['tag']}")
                      for domain in rule['domains']:
                          print(f"      • {domain}")
                  print()
              
              if analysis['ip_rules']:
                  print("ПРАВИЛА ПО IP:")
                  for rule in analysis['ip_rules']:
                      print(f"  [{rule['index']}] → {rule['tag']}")
                      for ip in rule['ips']:
                          print(f"      • {ip}")
                  print()
              
              # Поиск наших специфических правил
              our_rules = {
                  'geosite:category-gov-ru': False,
                  'regexp:.*\\.ru$': False,
                  'geosite:openai': False,
                  'geoip:ru': False
              }
              
              for rule in analysis['detailed_rules']:
                  for domain in rule['domains']:
                      if domain in our_rules:
                          our_rules[domain] = True
                  for ip in rule['ips']:
                      if ip in our_rules:
                          our_rules[ip] = True
              
              print("СТАТУС НАШИХ ПРАВИЛ:")
              print(f"  • geosite:category-gov-ru: {'✅ Настроено' if our_rules['geosite:category-gov-ru'] else '❌ Отсутствует'}")
              print(f"  • regexp:.*\\.ru$: {'✅ Настроено' if our_rules['regexp:.*\\.ru$'] else '❌ Отсутствует'}")
              print(f"  • geosite:openai: {'✅ Настроено' if our_rules['geosite:openai'] else '❌ Отсутствует'}")
              print(f"  • geoip:ru: {'✅ Настроено' if our_rules['geoip:ru'] else '❌ Отсутствует'}")
              
              return analysis
        dest: /tmp/analyze_routing.py
        mode: '0755'

    - name: Выполнить детальный анализ правил маршрутизации
      command: python3 /tmp/analyze_routing.py "{{ xray_config_path }}"
      register: routing_analysis
      changed_when: false

    - name: Показать результат анализа
      debug:
        msg: "{{ routing_analysis.stdout_lines }}"

    - name: Проверить статус x-ui
      systemd:
        name: x-ui
      register: service_status

    - name: Создать отчет о текущей конфигурации маршрутизации
      copy:
        content: |
          ==========================================
          📊 ОТЧЕТ О МАРШРУТИЗАЦИИ XRAY
          ==========================================
          
          📅 Дата анализа: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          📁 Конфигурация: {{ xray_config_path }}
          
          ==========================================
          📈 ОБЩАЯ СТАТИСТИКА
          ==========================================
          
          Всего правил маршрутизации: {{ routing_rules | length }}
          Правил с direct маршрутом: {{ direct_rules | length }}
          Правил с proxy маршрутом: {{ proxy_rules | length }}
          Правил по доменам: {{ domain_rules | length }}
          Правил по IP: {{ ip_rules | length }}
          
          ==========================================
          🔧 СТАТУС СЕРВИСА
          ==========================================
          
          Статус x-ui: {{ service_status.status.ActiveState }}
          Включен при загрузке: {{ service_status.status.UnitFileState }}
          
          ==========================================
          📋 ДЕТАЛЬНЫЙ АНАЛИЗ
          ==========================================
          
          {{ routing_analysis.stdout }}
          
          ==========================================
          🛠️ УПРАВЛЕНИЕ ПРАВИЛАМИ
          ==========================================
          
          Для добавления новых правил:
          ansible-playbook -i inventory.ini configure_xray_routing.yml
          
          Для просмотра конфигурации:
          jq '.routing.rules' {{ xray_config_path }}
          
          Для резервного копирования:
          cp {{ xray_config_path }} {{ backup_dir }}/config.json.manual.$(date +%s)
          
          ==========================================
          📖 ОПИСАНИЕ ПРАВИЛ
          ==========================================
          
          {% for rule in direct_rules %}
          Правило {{ loop.index }}:
          {% if rule.domain is defined %}
            Домены: {{ rule.domain | join(', ') }}
          {% endif %}
          {% if rule.ip is defined %}
            IP: {{ rule.ip | join(', ') }}
          {% endif %}
            Маршрут: {{ rule.outboundTag }}
          
          {% endfor %}
          ==========================================
        dest: "{{ backup_dir }}/ROUTING_ANALYSIS_REPORT.txt"
        mode: "0644"

    - name: Удалить временный скрипт
      file:
        path: /tmp/analyze_routing.py
        state: absent

    - name: Показать краткую сводку
      debug:
        msg:
          - "📊 Анализ маршрутизации Xray завершен"
          - ""
          - "📈 Статистика:"
          - "  • Всего правил: {{ routing_rules | length }}"
          - "  • Direct правил: {{ direct_rules | length }}"
          - "  • Proxy правил: {{ proxy_rules | length }}"
          - "  • Правил по доменам: {{ domain_rules | length }}"
          - "  • Правил по IP: {{ ip_rules | length }}"
          - ""
          - "🔧 Статус x-ui: {{ service_status.status.ActiveState }}"
          - "📁 Конфигурация: {{ xray_config_path }}"
          - "📋 Отчет: {{ backup_dir }}/ROUTING_ANALYSIS_REPORT.txt"
          - ""
          - "💡 Для добавления правил запустите:"
          - "   ansible-playbook -i inventory.ini configure_xray_routing.yml"
