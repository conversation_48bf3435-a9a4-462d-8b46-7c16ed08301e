---
- name: Инструкции для ручного изменения 3x-ui
  hosts: webservers
  become: true
  vars:
    suggested_username: "admin"
    suggested_password: "admin123"
    suggested_port: 8443

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Проверить статус x-ui
      systemd:
        name: x-ui
      register: service_status

    - name: Создать инструкцию для ручного изменения
      copy:
        content: |
          ==========================================
          📋 ИНСТРУКЦИЯ: ИЗМЕНЕНИЕ УЧЕТНЫХ ДАННЫХ 3X-UI
          ==========================================

          🌐 IP сервера: {{ server_ip }}
          📅 Дата: {{ ansible_date_time.iso8601 }}
          🔧 Статус x-ui: {{ service_status.status.ActiveState }}

          ==========================================
          🔑 СПОСОБ 1: ЧЕРЕЗ КОМАНДУ x-ui
          ==========================================

          1. Подключитесь к серверу по SSH:
             ssh root@{{ server_ip }}

          2. Запустите команду настройки:
             x-ui

          3. В меню выберите опцию 6:
             "6. Reset username & password"

          4. Подтвердите сброс:
             "Are you sure to reset the username and password of the panel? [Default n]: y"

          5. Введите новый логин (рекомендуется):
             "Please set the login username [default is a random username]: {{ suggested_username }}"

          6. Введите новый пароль (рекомендуется):
             "Please set the login password [default is a random password]: {{ suggested_password }}"

          7. Отключите двухфакторную аутентификацию:
             "Do you want to disable currently configured two-factor authentication? (y/n): y"

          8. Выберите опцию 0 для выхода

          10. Перезапустите сервис:
              systemctl restart x-ui

          ==========================================
          🔑 СПОСОБ 2: ЧЕРЕЗ БАЗУ ДАННЫХ
          ==========================================

          1. Остановите x-ui:
             systemctl stop x-ui

          2. Создайте MD5 хеш пароля:
             echo -n "{{ suggested_password }}" | md5sum

          3. Обновите базу данных:
             sqlite3 /etc/x-ui/x-ui.db "UPDATE users SET username='{{ suggested_username }}', password='HASH_FROM_STEP_2' WHERE id=1;"

          4. Обновите порт:
             sqlite3 /etc/x-ui/x-ui.db "INSERT OR REPLACE INTO settings (key, value) VALUES ('webPort', '{{ suggested_port }}');"

          5. Запустите x-ui:
             systemctl start x-ui

          ==========================================
          🔑 СПОСОБ 3: СБРОС К ЗНАЧЕНИЯМ ПО УМОЛЧАНИЮ
          ==========================================

          1. Остановите x-ui:
             systemctl stop x-ui

          2. Удалите базу данных:
             rm /etc/x-ui/x-ui.db

          3. Запустите x-ui (создастся новая БД):
             systemctl start x-ui

          4. Учетные данные по умолчанию:
             Логин: admin
             Пароль: admin
             Порт: 2053

          ==========================================
          🔍 ПРОВЕРКА РЕЗУЛЬТАТА
          ==========================================

          1. Проверьте статус сервиса:
             systemctl status x-ui

          2. Проверьте открытые порты:
             netstat -tlnp | grep x-ui

          3. Посмотрите логи:
             journalctl -u x-ui -f

          4. Откройте панель в браузере:
             https://{{ server_ip }}:{{ suggested_port }}

          ==========================================
          🛡️ НАСТРОЙКА ФАЕРВОЛА
          ==========================================

          Не забудьте открыть новый порт в фаерволе:

          # UFW
          ufw allow {{ suggested_port }}/tcp

          # iptables
          iptables -A INPUT -p tcp --dport {{ suggested_port }} -j ACCEPT

          ==========================================
          📞 КОМАНДЫ ДЛЯ ДИАГНОСТИКИ
          ==========================================

          # Проверить настройки в базе данных
          sqlite3 /etc/x-ui/x-ui.db "SELECT key, value FROM settings WHERE key LIKE 'web%';"

          # Проверить пользователей
          sqlite3 /etc/x-ui/x-ui.db "SELECT id, username FROM users;"

          # Проверить процессы x-ui
          ps aux | grep x-ui

          # Проверить конфигурацию
          cat /usr/local/x-ui/bin/config.json

          ==========================================
        dest: "/root/cert/MANUAL_RESET_INSTRUCTIONS.txt"
        mode: "0644"

    - name: Показать краткую инструкцию
      debug:
        msg:
          - "📋 Инструкция создана: /root/cert/MANUAL_RESET_INSTRUCTIONS.txt"
          - ""
          - "🔧 Быстрый способ через SSH:"
          - "1. ssh root@{{ server_ip }}"
          - "2. x-ui"
          - "3. Выберите опцию 6 (Reset username & password)"
          - "4. Подтвердите: y"
          - "5. Введите новый логин: {{ suggested_username }}"
          - "6. Введите новый пароль: {{ suggested_password }}"
          - "7. Отключите 2FA: y"
          - "8. Выберите опцию 0 (Exit)"
          - "9. systemctl restart x-ui"
          - ""
          - "🌐 После изменения панель будет доступна:"
          - "https://{{ server_ip }}:{{ suggested_port }}"

    - name: Показать текущие настройки из базы данных
      shell: |
        sqlite3 /etc/x-ui/x-ui.db "SELECT key, value FROM settings WHERE key LIKE 'web%' ORDER BY key;" 2>/dev/null || echo "Не удалось прочитать базу данных"
      register: current_db_settings
      changed_when: false
      ignore_errors: yes

    - name: Показать текущие настройки
      debug:
        msg:
          - "📊 Текущие настройки в базе данных:"
          - "{{ current_db_settings.stdout_lines }}"

    - name: Показать текущих пользователей
      shell: |
        sqlite3 /etc/x-ui/x-ui.db "SELECT id, username FROM users;" 2>/dev/null || echo "Не удалось прочитать пользователей"
      register: current_users
      changed_when: false
      ignore_errors: yes

    - name: Показать пользователей
      debug:
        msg:
          - "👥 Текущие пользователи:"
          - "{{ current_users.stdout_lines }}"
