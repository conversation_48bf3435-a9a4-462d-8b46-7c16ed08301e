---
- name: <PERSON>бр<PERSON><PERSON>ить 3x-ui к настройкам по умолчанию
  hosts: webservers
  become: true
  vars:
    default_username: "admin"
    default_password: "admin"
    default_port: 2053

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Остановить сервис x-ui
      systemd:
        name: x-ui
        state: stopped

    - name: Создать резервную копию текущей базы данных
      copy:
        src: /etc/x-ui/x-ui.db
        dest: /etc/x-ui/x-ui.db.backup.{{ ansible_date_time.epoch }}
        remote_src: yes
      ignore_errors: yes

    - name: Удалить текущую базу данных
      file:
        path: /etc/x-ui/x-ui.db
        state: absent

    - name: Запустить x-ui для создания новой базы данных
      systemd:
        name: x-ui
        state: started

    - name: Подождать создания новой базы данных
      wait_for:
        path: /etc/x-ui/x-ui.db
        timeout: 30

    - name: Подождать запуска сервиса
      wait_for:
        port: "{{ default_port }}"
        host: "0.0.0.0"
        delay: 5
        timeout: 30

    - name: Проверить доступность панели
      uri:
        url: "http://{{ server_ip }}:{{ default_port }}"
        method: GET
        timeout: 10
      register: panel_check
      ignore_errors: yes

    - name: Проверить настройки в новой базе данных
      shell: |
        sqlite3 /etc/x-ui/x-ui.db "SELECT key, value FROM settings WHERE key LIKE 'web%' ORDER BY key;" 2>/dev/null || echo "База данных недоступна"
      register: new_settings
      changed_when: false

    - name: Проверить пользователей в новой базе данных
      shell: |
        sqlite3 /etc/x-ui/x-ui.db "SELECT id, username FROM users;" 2>/dev/null || echo "Пользователи недоступны"
      register: new_users
      changed_when: false

    - name: Создать отчет о сбросе
      copy:
        content: |
          ==========================================
          🔄 3X-UI СБРОШЕН К НАСТРОЙКАМ ПО УМОЛЧАНИЮ
          ==========================================
          
          📅 Дата сброса: {{ ansible_date_time.iso8601 }}
          🌐 IP сервера: {{ server_ip }}
          
          ==========================================
          🔑 УЧЕТНЫЕ ДАННЫЕ ПО УМОЛЧАНИЮ
          ==========================================
          
          🔗 URL панели: http://{{ server_ip }}:{{ default_port }}
          👤 Логин: {{ default_username }}
          🔑 Пароль: {{ default_password }}
          🔧 Порт: {{ default_port }}
          
          ==========================================
          📊 НОВЫЕ НАСТРОЙКИ В БАЗЕ ДАННЫХ
          ==========================================
          
          {{ new_settings.stdout }}
          
          ==========================================
          👥 ПОЛЬЗОВАТЕЛИ
          ==========================================
          
          {{ new_users.stdout }}
          
          ==========================================
          💾 РЕЗЕРВНАЯ КОПИЯ
          ==========================================
          
          Старая база данных сохранена:
          /etc/x-ui/x-ui.db.backup.{{ ansible_date_time.epoch }}
          
          Для восстановления старых настроек:
          1. systemctl stop x-ui
          2. cp /etc/x-ui/x-ui.db.backup.{{ ansible_date_time.epoch }} /etc/x-ui/x-ui.db
          3. systemctl start x-ui
          
          ==========================================
          🚀 СЛЕДУЮЩИЕ ШАГИ
          ==========================================
          
          1. Откройте браузер: http://{{ server_ip }}:{{ default_port }}
          2. Войдите с учетными данными по умолчанию:
             Логин: {{ default_username }}
             Пароль: {{ default_password }}
          3. Измените пароль в настройках панели
          4. Настройте SSL сертификаты
          5. Измените порт для безопасности
          
          ==========================================
          ⚠️  ВАЖНЫЕ ЗАМЕЧАНИЯ
          ==========================================
          
          • Панель работает по HTTP (не HTTPS)
          • Используются стандартные учетные данные
          • Рекомендуется сразу изменить пароль
          • Настройте SSL для безопасности
          
          ==========================================
          🔧 КОМАНДЫ ДЛЯ НАСТРОЙКИ
          ==========================================
          
          # Изменить настройки через меню
          x-ui
          
          # Проверить статус
          systemctl status x-ui
          
          # Посмотреть логи
          journalctl -u x-ui -f
          
          # Настроить фаервол
          ufw allow {{ default_port }}/tcp
          
          ==========================================
        dest: "/root/cert/RESET_TO_DEFAULT_REPORT.txt"
        mode: "0644"

    - name: Показать результат сброса
      debug:
        msg:
          - "🔄 Сброс к настройкам по умолчанию завершен!"
          - ""
          - "🔗 URL: http://{{ server_ip }}:{{ default_port }}"
          - "👤 Логин: {{ default_username }}"
          - "🔑 Пароль: {{ default_password }}"
          - ""
          - "🌐 Панель {{ 'доступна' if panel_check.status == 200 else 'может быть недоступна' }}"
          - "💾 Резервная копия: /etc/x-ui/x-ui.db.backup.{{ ansible_date_time.epoch }}"
          - "📋 Отчет: /root/cert/RESET_TO_DEFAULT_REPORT.txt"
          - ""
          - "⚠️  ВАЖНО: Сразу измените пароль и настройте SSL!"
