- name: Установка и настройка Logwatch
  hosts: webservers
  become: true

  vars:
    logwatch_email: "<EMAIL>" # <-- замени на свой email

  tasks:
    - name: Установить logwatch
      apt:
        name: logwatch
        state: present
        update_cache: yes

    - name: Настроить email для отчетов logwatch
      lineinfile:
        path: /etc/logwatch/conf/logwatch.conf
        regexp: "^MailTo ="
        line: "MailTo = {{ logwatch_email }}"
        create: yes

    - name: Убедиться, что cron для logwatch включен
      cron:
        name: "Logwatch daily report"
        job: "/usr/sbin/logwatch --output mail"
        user: root
        minute: "0"
        hour: "0"
        state: present
