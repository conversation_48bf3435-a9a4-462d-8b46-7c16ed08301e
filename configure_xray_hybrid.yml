---
- name: Ги<PERSON><PERSON>идная настройка маршрутизаци<PERSON> (API + файл)
  hosts: webservers
  become: true
  vars:
    panel_url: "http://************:23456/IxIPY9iNxOOpf9Zb6n"
    panel_username: "fSkACNeS"
    panel_password: "imGecmwscOT7Pm"
    xray_config_path: "/usr/local/x-ui/bin/config.json"
    backup_dir: "/root/cert/xray_backups"
    
    routing_rules:
      - type: "field"
        domain:
          - "geosite:category-gov-ru"
        outboundTag: "direct"
        comment: "Russian government sites"
      - type: "field"
        domain:
          - "regexp:.*\\.ru$"
        outboundTag: "direct"
        comment: "All .ru domains"
      - type: "field"
        domain:
          - "geosite:openai"
        outboundTag: "direct"
        comment: "OpenAI services"
      - type: "field"
        ip:
          - "geoip:ru"
        outboundTag: "direct"
        comment: "Russian IP addresses"

  tasks:
    - name: По<PERSON>у<PERSON>ить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать директорию для резервных копий
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: "0700"

    - name: Установить необходимые пакеты
      apt:
        name:
          - jq
          - python3
        state: present

    - name: Попробовать подключиться к API панели
      uri:
        url: "{{ panel_url }}/login"
        method: POST
        body_format: form-urlencoded
        body:
          username: "{{ panel_username }}"
          password: "{{ panel_password }}"
        timeout: 30
        status_code: [200, 302]
        follow_redirects: no
      register: login_response
      ignore_errors: yes

    - name: Показать статус API подключения
      debug:
        msg:
          - "🔐 API подключение: {{ 'Успешно' if login_response.status in [200, 302] else 'Ошибка' }}"
          - "📊 HTTP статус: {{ login_response.status | default('N/A') }}"

    - name: Попробовать найти рабочий API эндпоинт для конфигурации
      uri:
        url: "{{ panel_url }}{{ item }}"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200]
      register: config_api_test
      ignore_errors: yes
      loop:
        - "/panel/api/xray/config"
        - "/api/xray/config"
        - "/xray/config"
        - "/panel/xray/config"
        - "/panel/api/config"
        - "/api/config"
      when: login_response.status is defined and login_response.status in [200, 302]

    - name: Определить метод настройки
      set_fact:
        use_api: "{{ config_api_test.results | default([]) | selectattr('status', 'equalto', 200) | list | length > 0 }}"
        working_api_endpoint: "{{ (config_api_test.results | default([]) | selectattr('status', 'equalto', 200) | list | first).item if config_api_test.results | default([]) | selectattr('status', 'equalto', 200) | list | length > 0 else '' }}"

    - name: Показать выбранный метод
      debug:
        msg:
          - "🔧 Выбранный метод настройки:"
          - "  • API доступен: {{ 'Да' if use_api else 'Нет' }}"
          - "  {% if use_api %}• Рабочий эндпоинт: {{ working_api_endpoint }}{% endif %}"
          - "  • Будет использован: {{ 'API' if use_api else 'Прямое редактирование файла' }}"

    # API метод
    - name: Получить текущую конфигурацию через API
      uri:
        url: "{{ panel_url }}{{ working_api_endpoint }}"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200]
      register: api_config
      when: use_api

    - name: Обновить конфигурацию через API
      block:
        - name: Создать Python скрипт для API обновления
          copy:
            content: |
              #!/usr/bin/env python3
              import json
              import requests
              import sys
              from urllib.parse import urljoin
              
              def update_config_via_api(base_url, endpoint, cookies, config, new_rules):
                  session = requests.Session()
                  session.cookies.update(cookies)
                  
                  # Добавляем правила
                  if 'routing' not in config:
                      config['routing'] = {'rules': []}
                  if 'rules' not in config['routing']:
                      config['routing']['rules'] = []
                  
                  existing_rules = config['routing']['rules']
                  added_count = 0
                  
                  for rule in new_rules:
                      rule_exists = False
                      for existing_rule in existing_rules:
                          if ('domain' in rule and 'domain' in existing_rule and 
                              set(rule['domain']) == set(existing_rule['domain'])):
                              rule_exists = True
                              break
                          if ('ip' in rule and 'ip' in existing_rule and 
                              set(rule['ip']) == set(existing_rule['ip'])):
                              rule_exists = True
                              break
                      
                      if not rule_exists:
                          rule_to_add = {k: v for k, v in rule.items() if k != 'comment'}
                          existing_rules.append(rule_to_add)
                          added_count += 1
                          print(f"Added rule: {rule.get('comment', 'No description')}")
                      else:
                          print(f"Rule exists: {rule.get('comment', 'No description')}")
                  
                  # Обновляем конфигурацию
                  update_url = urljoin(base_url, endpoint)
                  response = session.post(update_url, json=config, 
                                        headers={'Content-Type': 'application/json'}, 
                                        timeout=30)
                  
                  if response.status_code == 200:
                      print(f"API update successful. Added {added_count} rules.")
                      return True
                  else:
                      print(f"API update failed: {response.status_code}")
                      return False
              
              if __name__ == "__main__":
                  # Читаем аргументы из файлов
                  with open('/tmp/api_config.json', 'r') as f:
                      config = json.load(f)
                  with open('/tmp/api_rules.json', 'r') as f:
                      new_rules = json.load(f)
                  with open('/tmp/api_params.json', 'r') as f:
                      params = json.load(f)
                  
                  success = update_config_via_api(
                      params['base_url'], 
                      params['endpoint'], 
                      params['cookies'], 
                      config, 
                      new_rules
                  )
                  
                  sys.exit(0 if success else 1)
            dest: /tmp/api_updater.py
            mode: "0755"

        - name: Создать файлы параметров для API
          copy:
            content: "{{ item.content | to_nice_json }}"
            dest: "{{ item.dest }}"
          loop:
            - { content: "{{ api_config.json }}", dest: "/tmp/api_config.json" }
            - { content: "{{ routing_rules }}", dest: "/tmp/api_rules.json" }
            - { content: { "base_url": "{{ panel_url }}", "endpoint": "{{ working_api_endpoint }}", "cookies": "{{ login_response.cookies | default({}) }}" }, dest: "/tmp/api_params.json" }

        - name: Выполнить обновление через API
          command: python3 /tmp/api_updater.py
          register: api_update_result

        - name: Показать результат API обновления
          debug:
            msg: "{{ api_update_result.stdout_lines }}"

      when: use_api

    # Файловый метод (fallback)
    - name: Обновить конфигурацию через файл
      block:
        - name: Создать резервную копию конфигурации
          copy:
            src: "{{ xray_config_path }}"
            dest: "{{ backup_dir }}/config.json.backup.{{ ansible_date_time.epoch }}"
            remote_src: yes
            mode: "0600"

        - name: Создать Python скрипт для файлового обновления
          copy:
            content: |
              #!/usr/bin/env python3
              import json
              import sys
              
              def update_config_file(config_file, rules_file):
                  with open(config_file, 'r', encoding='utf-8') as f:
                      config = json.load(f)
                  
                  with open(rules_file, 'r', encoding='utf-8') as f:
                      new_rules = json.load(f)
                  
                  if 'routing' not in config:
                      config['routing'] = {'rules': []}
                  if 'rules' not in config['routing']:
                      config['routing']['rules'] = []
                  
                  existing_rules = config['routing']['rules']
                  added_count = 0
                  
                  for rule in new_rules:
                      rule_exists = False
                      for existing_rule in existing_rules:
                          if ('domain' in rule and 'domain' in existing_rule and 
                              set(rule['domain']) == set(existing_rule['domain'])):
                              rule_exists = True
                              break
                          if ('ip' in rule and 'ip' in existing_rule and 
                              set(rule['ip']) == set(existing_rule['ip'])):
                              rule_exists = True
                              break
                      
                      if not rule_exists:
                          rule_to_add = {k: v for k, v in rule.items() if k != 'comment'}
                          existing_rules.append(rule_to_add)
                          added_count += 1
                          print(f"Added rule: {rule.get('comment', 'No description')}")
                      else:
                          print(f"Rule exists: {rule.get('comment', 'No description')}")
                  
                  with open(config_file, 'w', encoding='utf-8') as f:
                      json.dump(config, f, indent=2, ensure_ascii=False)
                  
                  print(f"File update successful. Added {added_count} rules.")
                  return added_count
              
              if __name__ == "__main__":
                  config_file = sys.argv[1]
                  rules_file = sys.argv[2]
                  update_config_file(config_file, rules_file)
            dest: /tmp/file_updater.py
            mode: "0755"

        - name: Создать файл с правилами
          copy:
            content: "{{ routing_rules | to_nice_json }}"
            dest: /tmp/file_rules.json

        - name: Выполнить обновление файла
          command: python3 /tmp/file_updater.py "{{ xray_config_path }}" /tmp/file_rules.json
          register: file_update_result

        - name: Показать результат файлового обновления
          debug:
            msg: "{{ file_update_result.stdout_lines }}"

        - name: Перезапустить x-ui для применения изменений
          systemd:
            name: x-ui
            state: restarted

      when: not use_api

    - name: Подождать применения изменений
      pause:
        seconds: 5

    - name: Проверить статус x-ui
      systemd:
        name: x-ui
      register: service_status

    - name: Создать отчет о гибридной настройке
      copy:
        content: |
          ==========================================
          🔄 ОТЧЕТ О ГИБРИДНОЙ НАСТРОЙКЕ XRAY
          ==========================================
          
          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          
          ==========================================
          🔧 ИСПОЛЬЗОВАННЫЙ МЕТОД
          ==========================================
          
          Метод: {{ 'API' if use_api else 'Прямое редактирование файла' }}
          {% if use_api %}
          API эндпоинт: {{ working_api_endpoint }}
          API статус: {{ 'Успешно' if api_update_result.rc == 0 else 'Ошибка' }}
          {% else %}
          Файл конфигурации: {{ xray_config_path }}
          Файловое обновление: {{ 'Успешно' if file_update_result.rc == 0 else 'Ошибка' }}
          Резервная копия: {{ backup_dir }}/config.json.backup.{{ ansible_date_time.epoch }}
          {% endif %}
          
          ==========================================
          📊 РЕЗУЛЬТАТ ОБНОВЛЕНИЯ
          ==========================================
          
          {% if use_api %}
          {{ api_update_result.stdout | default('Нет вывода') }}
          {% else %}
          {{ file_update_result.stdout | default('Нет вывода') }}
          {% endif %}
          
          ==========================================
          🔧 СТАТУС СЕРВИСА
          ==========================================
          
          Статус x-ui: {{ service_status.status.ActiveState }}
          
          ==========================================
          ✅ ДОБАВЛЕННЫЕ ПРАВИЛА
          ==========================================
          
          1. 🏛️ geosite:category-gov-ru → direct
          2. 🇷🇺 regexp:.*\.ru$ → direct
          3. 🤖 geosite:openai → direct
          4. 🌐 geoip:ru → direct
          
          ==========================================
          💡 ПРЕИМУЩЕСТВА ГИБРИДНОГО ПОДХОДА
          ==========================================
          
          • Автоматический выбор лучшего метода
          • Fallback на файловое редактирование
          • Максимальная совместимость
          • Надежное применение изменений
          
          ==========================================
        dest: "{{ backup_dir }}/HYBRID_CONFIGURATION_REPORT.txt"
        mode: "0644"

    - name: Удалить временные файлы
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /tmp/api_updater.py
        - /tmp/api_config.json
        - /tmp/api_rules.json
        - /tmp/api_params.json
        - /tmp/file_updater.py
        - /tmp/file_rules.json

    - name: Показать финальный результат
      debug:
        msg:
          - "🔄 Гибридная настройка завершена!"
          - ""
          - "🔧 Использованный метод: {{ 'API' if use_api else 'Файловое редактирование' }}"
          - "📊 Результат: {{ 'Успешно' if (use_api and api_update_result.rc == 0) or (not use_api and file_update_result.rc == 0) else 'Ошибка' }}"
          - "🔧 Статус x-ui: {{ service_status.status.ActiveState }}"
          - ""
          - "✅ Добавленные правила:"
          - "  • 🏛️ geosite:category-gov-ru → direct"
          - "  • 🇷🇺 regexp:.*\\.ru$ → direct"
          - "  • 🤖 geosite:openai → direct"
          - "  • 🌐 geoip:ru → direct"
          - ""
          - "📋 Отчет: {{ backup_dir }}/HYBRID_CONFIGURATION_REPORT.txt"
