---
- name: <PERSON>з<PERSON><PERSON><PERSON><PERSON><PERSON>ь логин и пароль 3x-ui через терминал
  hosts: webservers
  become: true
  vars:
    new_username: "admin"
    new_password: "admin123"
    new_port: 8443

  tasks:
    - name: По<PERSON><PERSON><PERSON><PERSON><PERSON>ь IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Установить expect для автоматизации
      apt:
        name: expect
        state: present

    - name: Создать скрипт для изменения учетных данных
      copy:
        content: |
          #!/usr/bin/expect -f
          
          # Запускаем x-ui
          spawn x-ui
          
          # Ждем главное меню
          expect "Please choose an option:"
          
          # Выбираем опцию 7 (Reset username & password)
          send "7\r"
          
          # Ждем запрос нового логина
          expect "Please set up the panel username:"
          send "{{ new_username }}\r"
          
          # Ждем запрос нового пароля
          expect "Please set up the panel password:"
          send "{{ new_password }}\r"
          
          # Ждем подтверждения
          expect "username and password set successfully"
          
          # Ждем возврат в главное меню
          expect "Please choose an option:"
          
          # Выбираем опцию 8 (Reset panel settings)
          send "8\r"
          
          # Ждем запрос порта
          expect "Please set up the panel port:"
          send "{{ new_port }}\r"
          
          # Пропускаем остальные настройки (Enter для значений по умолчанию)
          expect "Please set up the panel username:"
          send "\r"
          
          expect "Please set up the panel password:"
          send "\r"
          
          expect "Please set up the panel path:"
          send "\r"
          
          # Ждем подтверждения
          expect "panel settings set successfully"
          
          # Ждем главное меню
          expect "Please choose an option:"
          
          # Выходим (опция 0)
          send "0\r"
          
          expect eof
        dest: /tmp/change_credentials.exp
        mode: '0755'

    - name: Запустить скрипт изменения учетных данных
      command: /tmp/change_credentials.exp
      register: credential_change
      ignore_errors: yes

    - name: Показать результат изменения
      debug:
        var: credential_change.stdout_lines

    - name: Перезапустить x-ui для применения изменений
      systemd:
        name: x-ui
        state: restarted

    - name: Подождать запуска сервиса
      wait_for:
        port: "{{ new_port }}"
        host: "0.0.0.0"
        delay: 5
        timeout: 30

    - name: Проверить статус сервиса
      systemd:
        name: x-ui
      register: service_status

    - name: Создать отчет об изменении учетных данных
      copy:
        content: |
          ==========================================
          ✅ УЧЕТНЫЕ ДАННЫЕ 3X-UI ИЗМЕНЕНЫ
          ==========================================
          
          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 IP сервера: {{ server_ip }}
          
          ==========================================
          🔑 НОВЫЕ УЧЕТНЫЕ ДАННЫЕ
          ==========================================
          
          🔗 URL панели: https://{{ server_ip }}:{{ new_port }}
          👤 Логин: {{ new_username }}
          🔑 Пароль: {{ new_password }}
          🔧 Порт: {{ new_port }}
          
          ==========================================
          📊 СТАТУС СЕРВИСА
          ==========================================
          
          Статус: {{ service_status.status.ActiveState }}
          Включен при загрузке: {{ service_status.status.UnitFileState }}
          
          ==========================================
          🚀 СЛЕДУЮЩИЕ ШАГИ
          ==========================================
          
          1. Откройте браузер: https://{{ server_ip }}:{{ new_port }}
          2. Войдите с новыми учетными данными:
             Логин: {{ new_username }}
             Пароль: {{ new_password }}
          3. Настройте фаервол: ufw allow {{ new_port }}/tcp
          
          ==========================================
          📋 ДОПОЛНИТЕЛЬНЫЕ КОМАНДЫ
          ==========================================
          
          # Проверить статус x-ui
          systemctl status x-ui
          
          # Посмотреть логи
          journalctl -u x-ui -f
          
          # Изменить настройки вручную
          x-ui
          
          ==========================================
        dest: "/root/cert/CREDENTIALS_CHANGED.txt"
        mode: "0600"

    - name: Удалить временный скрипт
      file:
        path: /tmp/change_credentials.exp
        state: absent

    - name: Показать финальный результат
      debug:
        msg:
          - "✅ Учетные данные изменены!"
          - ""
          - "🔗 URL: https://{{ server_ip }}:{{ new_port }}"
          - "👤 Логин: {{ new_username }}"
          - "🔑 Пароль: {{ new_password }}"
          - ""
          - "🔧 Статус x-ui: {{ service_status.status.ActiveState }}"
          - "📋 Отчет: /root/cert/CREDENTIALS_CHANGED.txt"
          - ""
          - "⚠️  Настройте фаервол: ufw allow {{ new_port }}/tcp"
