---
- name: <PERSON>б<PERSON>ружение API эндпоинтов 3x-ui панели
  hosts: webservers
  become: true
  vars:
    panel_url: "http://************:23456/IxIPY9iNxOOpf9Zb6n"
    panel_username: "fSkACNeS"
    panel_password: "imGecmwscOT7Pm"
    backup_dir: "/root/cert/xray_backups"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать директорию для отчетов
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: "0700"

    - name: Аутентификация в панели 3x-ui
      uri:
        url: "{{ panel_url }}/login"
        method: POST
        body_format: form-urlencoded
        body:
          username: "{{ panel_username }}"
          password: "{{ panel_password }}"
        timeout: 30
        status_code: [200, 302]
        follow_redirects: no
      register: login_response

    - name: Показать результат аутентификации
      debug:
        msg:
          - "🔐 Аутентификация: {{ 'Успешно' if login_response.status in [200, 302] else 'Ошибка' }}"
          - "📊 HTTP статус: {{ login_response.status }}"

    - name: Попробовать различные API эндпоинты для конфигурации
      uri:
        url: "{{ panel_url }}{{ item }}"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200, 404, 403, 401]
      register: api_tests
      ignore_errors: yes
      loop:
        - "/panel/api/xray/config"
        - "/api/xray/config"
        - "/xray/config"
        - "/panel/xray/config"
        - "/panel/api/config"
        - "/api/config"
        - "/config"
        - "/panel/settings"
        - "/settings"
        - "/panel/api/settings"
        - "/api/settings"
        - "/panel/xray"
        - "/xray"
        - "/panel/api/xray"
        - "/api/xray"

    - name: Показать результаты тестирования API эндпоинтов
      debug:
        msg: |
          🔍 Тестирование API эндпоинтов:
          {% for result in api_tests.results %}
          {{ result.item }}: {{ result.status | default('Ошибка') }} ({{ 'Успех' if result.status == 200 else 'Недоступен' }})
          {% endfor %}

    - name: Найти успешные эндпоинты
      set_fact:
        successful_endpoints: "{{ api_tests.results | selectattr('status', 'equalto', 200) | map(attribute='item') | list }}"

    - name: Показать успешные эндпоинты
      debug:
        msg:
          - "✅ Успешные API эндпоинты:"
          - "{{ successful_endpoints }}"

    - name: Попробовать получить содержимое успешных эндпоинтов
      uri:
        url: "{{ panel_url }}{{ item }}"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200]
      register: endpoint_content
      ignore_errors: yes
      loop: "{{ successful_endpoints }}"
      when: successful_endpoints | length > 0

    - name: Показать содержимое успешных эндпоинтов
      debug:
        msg: |
          📋 Содержимое {{ item.item }}:
          📏 Размер: {{ item.content | default('') | length }} символов
          📄 Тип: {{ item.content_type | default('unknown') }}
          {% if item.content | default('') | length < 500 %}Содержимое: {{ item.content | default('Пусто') }}{% endif %}
      loop: "{{ endpoint_content.results | default([]) }}"
      when: endpoint_content is defined

    - name: Попробовать эндпоинты для inbound конфигурации
      uri:
        url: "{{ panel_url }}{{ item }}"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200, 404, 403]
      register: inbound_tests
      ignore_errors: yes
      loop:
        - "/panel/api/inbounds/list"
        - "/api/inbounds/list"
        - "/inbounds/list"
        - "/panel/inbounds"
        - "/inbounds"
        - "/panel/api/inbounds"
        - "/api/inbounds"

    - name: Показать результаты inbound эндпоинтов
      debug:
        msg: |
          📡 Тестирование inbound эндпоинтов:
          {% for result in inbound_tests.results %}
          {{ result.item }}: {{ result.status | default('Ошибка') }} {% if result.status == 200 %}({{ result.json | default([]) | length }} записей){% endif %}
          {% endfor %}

    - name: Попробовать получить главную страницу панели
      uri:
        url: "{{ panel_url }}/panel"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200, 404]
      register: panel_main
      ignore_errors: yes

    - name: Анализировать HTML главной страницы для поиска API путей
      shell: |
        echo "{{ panel_main.content | default('') }}" | grep -oE '/(api|panel)/[^"'\''[:space:]]*' | sort | uniq || echo "Не найдено API путей"
      register: html_api_paths
      when: panel_main.status is defined and panel_main.status == 200
      changed_when: false

    - name: Показать найденные API пути в HTML
      debug:
        msg: |
          🔍 API пути найденные в HTML:
          {{ html_api_paths.stdout_lines | default(['Не найдено']) | join('\n') }}
      when: html_api_paths is defined

    - name: Попробовать альтернативные методы получения конфигурации
      uri:
        url: "{{ panel_url }}{{ item.path }}"
        method: "{{ item.method }}"
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
          Content-Type: "application/json"
        body: "{{ item.body | default(omit) }}"
        body_format: "{{ 'json' if item.body is defined else 'form-urlencoded' }}"
        timeout: 30
        status_code: [200, 404, 405, 403]
      register: alt_config_tests
      ignore_errors: yes
      loop:
        - { path: "/server/status", method: "GET" }
        - { path: "/panel/server/status", method: "GET" }
        - { path: "/panel/api/server/status", method: "GET" }
        - { path: "/api/server/status", method: "GET" }
        - { path: "/panel/xray/restart", method: "POST" }
        - { path: "/xray/restart", method: "POST" }

    - name: Показать результаты альтернативных тестов
      debug:
        msg: |
          🔄 Альтернативные эндпоинты:
          {% for result in alt_config_tests.results %}
          {{ result.item.method }} {{ result.item.path }}: {{ result.status | default('Ошибка') }}
          {% endfor %}

    - name: Создать отчет об обнаружении API
      copy:
        content: |
          ==========================================
          🔍 ОТЧЕТ ОБ ОБНАРУЖЕНИИ API 3X-UI
          ==========================================

          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          📡 URL панели: {{ panel_url }}

          ==========================================
          🔐 АУТЕНТИФИКАЦИЯ
          ==========================================

          Статус: {{ 'Успешно' if login_response.status in [200, 302] else 'Ошибка' }}
          HTTP код: {{ login_response.status }}

          ==========================================
          ✅ УСПЕШНЫЕ API ЭНДПОИНТЫ
          ==========================================

          {% for endpoint in successful_endpoints %}
          {{ endpoint }}
          {% endfor %}
          {% if successful_endpoints | length == 0 %}
          Успешные эндпоинты не найдены
          {% endif %}

          ==========================================
          📡 INBOUND ЭНДПОИНТЫ
          ==========================================

          {% for result in inbound_tests.results %}
          {{ result.item }}: {{ result.status | default('Ошибка') }}{% if result.status == 200 %} ({{ result.json | default([]) | length }} записей){% endif %}
          {% endfor %}

          ==========================================
          🔍 API ПУТИ ИЗ HTML
          ==========================================

          {% if html_api_paths is defined %}
          {{ html_api_paths.stdout }}
          {% else %}
          HTML анализ недоступен
          {% endif %}

          ==========================================
          🔄 АЛЬТЕРНАТИВНЫЕ ЭНДПОИНТЫ
          ==========================================

          {% for result in alt_config_tests.results %}
          {{ result.item.method }} {{ result.item.path }}: {{ result.status | default('Ошибка') }}
          {% endfor %}

          ==========================================
          💡 РЕКОМЕНДАЦИИ
          ==========================================

          1. Проверьте документацию вашей версии 3x-ui
          2. Используйте браузерские инструменты разработчика для анализа API вызовов
          3. Рассмотрите возможность обновления 3x-ui до последней версии
          4. Попробуйте прямое редактирование файла конфигурации

          ==========================================
          📋 ТЕСТИРОВАННЫЕ ЭНДПОИНТЫ
          ==========================================

          {% for result in api_tests.results %}
          {{ result.item }}: {{ result.status | default('Ошибка') }}
          {% endfor %}

          ==========================================
        dest: "{{ backup_dir }}/API_DISCOVERY_REPORT.txt"
        mode: "0644"

    - name: Показать заключение
      debug:
        msg:
          - "🔍 Обнаружение API завершено"
          - ""
          - "✅ Результаты:"
          - "  • Аутентификация: {{ 'Успешно' if login_response.status in [200, 302] else 'Ошибка' }}"
          - "  • Успешных эндпоинтов: {{ successful_endpoints | length }}"
          - "  • Inbound API работает: {{ 'Да' if inbound_tests.results | selectattr('status', 'equalto', 200) | list | length > 0 else 'Нет' }}"
          - ""
          - "💡 Рекомендации:"
          - "  • Используйте браузер для анализа реальных API вызовов"
          - "  • Проверьте версию 3x-ui панели"
          - "  • Рассмотрите прямое редактирование конфигурации"
          - ""
          - "📋 Отчет: {{ backup_dir }}/API_DISCOVERY_REPORT.txt"
