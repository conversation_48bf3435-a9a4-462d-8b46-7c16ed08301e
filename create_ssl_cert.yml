---
- name: Создать SSL сертификаты для 3x-ui панели
  hosts: webservers
  become: true
  vars:
    cert_dir: "/root/cert"
    cert_validity_days: 365
    private_key_name: "secret.key"
    cert_request_name: "cert.csr"
    certificate_name: "cert.crt"

  tasks:
    - name: Установить OpenSSL (если не установлен)
      apt:
        name: openssl
        state: present
        update_cache: yes

    - name: Создать директорию для сертификатов
      file:
        path: "{{ cert_dir }}"
        state: directory
        mode: '0700'
        owner: root
        group: root

    - name: Создать закрытый ключ
      openssl_privatekey:
        path: "{{ cert_dir }}/{{ private_key_name }}"
        size: 2048
        mode: '0600'
        owner: root
        group: root

    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать запрос на подпись сертификата (CSR)
      openssl_csr:
        path: "{{ cert_dir }}/{{ cert_request_name }}"
        privatekey_path: "{{ cert_dir }}/{{ private_key_name }}"
        common_name: "{{ server_ip }}"
        country_name: "RU"
        state_or_province_name: "Moscow"
        locality_name: "Moscow"
        organization_name: "Private"
        organizational_unit_name: "IT"
        email_address: "admin@{{ server_ip }}"

    - name: Создать самоподписанный сертификат
      openssl_certificate:
        path: "{{ cert_dir }}/{{ certificate_name }}"
        privatekey_path: "{{ cert_dir }}/{{ private_key_name }}"
        csr_path: "{{ cert_dir }}/{{ cert_request_name }}"
        provider: selfsigned
        valid_in: "{{ cert_validity_days * 24 * 3600 }}"  # в секундах
        mode: '0644'
        owner: root
        group: root

    - name: Показать пути к созданным файлам
      debug:
        msg:
          - "Путь к файлу публичного ключа сертификата: {{ cert_dir }}/{{ certificate_name }}"
          - "Путь к файлу приватного ключа сертификата: {{ cert_dir }}/{{ private_key_name }}"
          - "IP адрес сервера (Common Name): {{ server_ip }}"

    - name: Проверить созданные файлы
      stat:
        path: "{{ item }}"
      register: cert_files
      loop:
        - "{{ cert_dir }}/{{ private_key_name }}"
        - "{{ cert_dir }}/{{ cert_request_name }}"
        - "{{ cert_dir }}/{{ certificate_name }}"

    - name: Показать информацию о файлах
      debug:
        msg: "Файл {{ item.item }} создан: {{ item.stat.exists }}"
      loop: "{{ cert_files.results }}"

    - name: Показать содержимое сертификата
      command: openssl x509 -in "{{ cert_dir }}/{{ certificate_name }}" -text -noout
      register: cert_info
      changed_when: false

    - name: Вывести информацию о сертификате
      debug:
        var: cert_info.stdout_lines

    - name: Создать инструкцию для настройки 3x-ui
      copy:
        content: |
          Инструкция по настройке SSL сертификатов в 3x-ui панели:
          
          1. Откройте веб-интерфейс 3x-ui панели
          2. Перейдите в настройки панели
          3. Укажите следующие пути:
          
          Путь к файлу публичного ключа сертификата панели:
          {{ cert_dir }}/{{ certificate_name }}
          
          Путь к файлу приватного ключа сертификата панели:
          {{ cert_dir }}/{{ private_key_name }}
          
          4. Сохраните настройки и перезапустите панель
          5. Также добавьте эти ключи во вкладку "Подписка"
          
          IP адрес сервера: {{ server_ip }}
          Срок действия сертификата: {{ cert_validity_days }} дней
        dest: "{{ cert_dir }}/ssl_setup_instructions.txt"
        mode: '0644'

    - name: Показать финальную инструкцию
      debug:
        msg:
          - "SSL сертификаты успешно созданы!"
          - "Инструкция сохранена в файл: {{ cert_dir }}/ssl_setup_instructions.txt"
          - "Используйте следующие пути в настройках 3x-ui:"
          - "Публичный ключ: {{ cert_dir }}/{{ certificate_name }}"
          - "Приватный ключ: {{ cert_dir }}/{{ private_key_name }}"
