---
- name: Безопасное изменение логина и пароля 3x-ui с автогенерацией
  hosts: webservers
  become: true
  vars:
    # Credentials will be generated using Ansible's password lookup
    credentials_dir: "/root/cert"

  tasks:
    - name: Полу<PERSON>ить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Сгенерировать случайный логин (8 символов) через Ansible lookup
      set_fact:
        new_username: "{{ lookup('password', '/tmp/ansible_username chars=ascii_letters,digits length=8') }}"

    - name: Сгенерировать случайный пароль (14 символов) через Ansible lookup
      set_fact:
        new_password: "{{ lookup('password', '/tmp/ansible_password chars=ascii_letters,digits length=14') }}"

    - name: Показать сгенерированные учетные данные
      debug:
        msg:
          - "🎲 Автоматически сгенерированы безопасные учетные данные:"
          - "👤 Логин: {{ new_username }} ({{ new_username | length }} символов)"
          - "🔑 Пароль: {{ new_password }} ({{ new_password | length }} символов)"
          - "🔐 Метод: Ansible password lookup plugin"

    - name: Установить expect для автоматизации
      apt:
        name: expect
        state: present

    - name: Создать скрипт для изменения учетных данных с сгенерированными значениями
      copy:
        content: |
          #!/usr/bin/expect -f
          
          set timeout 30
          
          spawn x-ui
          
          expect {
              "Please enter your selection" {
                  send "6\r"
                  exp_continue
              }
              "Are you sure to reset the username and password of the panel?" {
                  send "y\r"
                  exp_continue
              }
              "Please set the login username" {
                  send "{{ new_username }}\r"
                  exp_continue
              }
              "Please set the login password" {
                  send "{{ new_password }}\r"
                  exp_continue
              }
              "Do you want to disable currently configured two-factor authentication?" {
                  send "y\r"
                  exp_continue
              }
              "Panel login username has been reset to:" {
                  expect "Please enter your selection"
                  send "0\r"
              }
              timeout {
                  puts "Timeout occurred during credential change"
                  exit 1
              }
              eof {
                  puts "Process completed successfully"
              }
          }
          
          expect eof
        dest: /tmp/secure_change_credentials.exp
        mode: '0755'

    - name: Выполнить изменение учетных данных
      command: /tmp/secure_change_credentials.exp
      register: change_result
      ignore_errors: yes

    - name: Показать результат выполнения
      debug:
        msg:
          - "📋 Результат изменения учетных данных:"
          - "{{ change_result.stdout_lines }}"

    - name: Перезапустить x-ui для применения изменений
      systemd:
        name: x-ui
        state: restarted

    - name: Подождать запуска сервиса
      pause:
        seconds: 5

    - name: Проверить статус сервиса
      systemd:
        name: x-ui
      register: service_status

    - name: Получить текущий порт из базы данных
      shell: |
        sqlite3 /etc/x-ui/x-ui.db "SELECT value FROM settings WHERE key='webPort';" 2>/dev/null || echo "2053"
      register: current_port
      changed_when: false

    - name: Установить порт для проверки
      set_fact:
        panel_port: "{{ current_port.stdout | default('2053') }}"

    - name: Проверить доступность панели
      uri:
        url: "http://{{ server_ip }}:{{ panel_port }}"
        method: GET
        timeout: 10
      register: panel_check
      ignore_errors: yes

    - name: Создать детальный отчет с безопасными учетными данными
      copy:
        content: |
          ==========================================
          🔐 БЕЗОПАСНЫЕ УЧЕТНЫЕ ДАННЫЕ 3X-UI
          ==========================================
          
          📅 Дата генерации: {{ ansible_date_time.iso8601 }}
          🌐 IP сервера: {{ server_ip }}
          🔧 Метод генерации: Ansible password lookup plugin
          
          ==========================================
          🎲 АВТОМАТИЧЕСКИ СГЕНЕРИРОВАННЫЕ ДАННЫЕ
          ==========================================
          
          🔗 URL панели: http://{{ server_ip }}:{{ panel_port }}
          👤 Логин: {{ new_username }}
          🔑 Пароль: {{ new_password }}
          🔧 Порт: {{ panel_port }}
          
          ==========================================
          🔐 ПАРАМЕТРЫ БЕЗОПАСНОСТИ
          ==========================================
          
          Логин:
          • Длина: {{ new_username | length }} символов (требуется: 8)
          • Содержит: латинские буквы (A-Z, a-z) + цифры (0-9)
          • Генератор: Ansible password lookup (криптографически стойкий)
          
          Пароль:
          • Длина: {{ new_password | length }} символов (требуется: 14)
          • Содержит: латинские буквы (A-Z, a-z) + цифры (0-9)
          • Генератор: Ansible password lookup (криптографически стойкий)
          
          ==========================================
          📊 СТАТУС СИСТЕМЫ
          ==========================================
          
          Сервис x-ui: {{ service_status.status.ActiveState }}
          Панель доступна: {{ 'Да' if panel_check.status == 200 else 'Проверьте вручную' }}
          Двухфакторная аутентификация: Отключена
          
          ==========================================
          🚀 ИНСТРУКЦИИ ПО ВХОДУ
          ==========================================
          
          1. Откройте браузер
          2. Перейдите по адресу: http://{{ server_ip }}:{{ panel_port }}
          3. Введите учетные данные:
             Логин: {{ new_username }}
             Пароль: {{ new_password }}
          4. Нажмите "Login"
          
          ==========================================
          🛡️ РЕКОМЕНДАЦИИ ПО БЕЗОПАСНОСТИ
          ==========================================
          
          1. Сохраните эти данные в менеджере паролей
          2. Настройте SSL сертификаты для HTTPS
          3. Измените порт с {{ panel_port }} на нестандартный
          4. Настройте фаервол: ufw allow {{ panel_port }}/tcp
          5. Регулярно создавайте резервные копии конфигурации
          
          ==========================================
          📋 ТЕХНИЧЕСКАЯ ИНФОРМАЦИЯ
          ==========================================
          
          Результат выполнения команды x-ui:
          {{ change_result.stdout }}
          
          Код возврата: {{ change_result.rc }}
          
          ==========================================
          ⚠️  ВАЖНЫЕ ПРЕДУПРЕЖДЕНИЯ
          ==========================================
          
          • ЭТИ УЧЕТНЫЕ ДАННЫЕ СГЕНЕРИРОВАНЫ СЛУЧАЙНО
          • ОНИ НЕ ПОВТОРЯТСЯ ПРИ СЛЕДУЮЩЕМ ЗАПУСКЕ
          • ОБЯЗАТЕЛЬНО СОХРАНИТЕ ИХ В БЕЗОПАСНОМ МЕСТЕ
          • ФАЙЛ ЗАЩИЩЕН ПРАВАМИ ДОСТУПА 600 (только root)
          
          ==========================================
        dest: "{{ credentials_dir }}/SECURE_GENERATED_CREDENTIALS.txt"
        mode: "0600"

    - name: Создать файл с учетными данными в формате переменных окружения
      copy:
        content: |
          # 3X-UI Secure Generated Credentials
          # Generated: {{ ansible_date_time.iso8601 }}
          # Server: {{ server_ip }}
          # Method: Ansible password lookup plugin
          
          export X_UI_URL="http://{{ server_ip }}:{{ panel_port }}"
          export X_UI_USERNAME="{{ new_username }}"
          export X_UI_PASSWORD="{{ new_password }}"
          export X_UI_PORT="{{ panel_port }}"
          
          # Credential specifications:
          # Username: {{ new_username | length }} chars (letters + digits)
          # Password: {{ new_password | length }} chars (letters + digits)
          
          # Usage:
          # source {{ credentials_dir }}/secure_credentials.env
          # echo "Login: $X_UI_USERNAME"
          # echo "Password: $X_UI_PASSWORD"
        dest: "{{ credentials_dir }}/secure_credentials.env"
        mode: "0600"

    - name: Создать JSON файл с учетными данными для автоматизации
      copy:
        content: |
          {
            "generated_at": "{{ ansible_date_time.iso8601 }}",
            "server_ip": "{{ server_ip }}",
            "panel": {
              "url": "http://{{ server_ip }}:{{ panel_port }}",
              "username": "{{ new_username }}",
              "password": "{{ new_password }}",
              "port": {{ panel_port }}
            },
            "security": {
              "username_length": {{ new_username | length }},
              "password_length": {{ new_password | length }},
              "generation_method": "ansible_password_lookup",
              "character_set": "ascii_letters_digits"
            },
            "status": {
              "service_active": "{{ service_status.status.ActiveState }}",
              "panel_accessible": {{ panel_check.status == 200 | lower }}
            }
          }
        dest: "{{ credentials_dir }}/secure_credentials.json"
        mode: "0600"

    - name: Удалить временные файлы
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /tmp/secure_change_credentials.exp
        - /tmp/ansible_username
        - /tmp/ansible_password

    - name: Показать финальный результат с безопасными учетными данными
      debug:
        msg:
          - "🔐 Безопасное изменение учетных данных завершено!"
          - ""
          - "🎲 АВТОМАТИЧЕСКИ СГЕНЕРИРОВАННЫЕ ДАННЫЕ:"
          - "🔗 URL: http://{{ server_ip }}:{{ panel_port }}"
          - "👤 Логин: {{ new_username }} ({{ new_username | length }} символов)"
          - "🔑 Пароль: {{ new_password }} ({{ new_password | length }} символов)"
          - ""
          - "🔧 Статус сервиса: {{ service_status.status.ActiveState }}"
          - "🌐 Доступность панели: {{ 'OK' if panel_check.status == 200 else 'Проверьте вручную' }}"
          - ""
          - "📋 Файлы с учетными данными созданы:"
          - "  • {{ credentials_dir }}/SECURE_GENERATED_CREDENTIALS.txt"
          - "  • {{ credentials_dir }}/secure_credentials.env"
          - "  • {{ credentials_dir }}/secure_credentials.json"
          - ""
          - "⚠️  КРИТИЧЕСКИ ВАЖНО:"
          - "⚠️  Сохраните эти данные в менеджере паролей!"
          - "⚠️  Данные сгенерированы криптографически стойким генератором!"
          - "⚠️  При следующем запуске будут созданы НОВЫЕ учетные данные!"
