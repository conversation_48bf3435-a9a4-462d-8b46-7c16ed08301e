- name: Установить Outline VPN и получить конфиг
  hosts: webservers
  become: true

  tasks:
    - name: Установить wget (если не установлен)
      apt:
        name: wget
        state: present
        update_cache: yes

    - name: Запустить скрипт установки Outline VPN
      shell: |
        sudo bash -c "$(wget -qO- https://raw.githubusercontent.com/Jigsaw-Code/outline-apps/master/server_manager/install_scripts/install_server.sh)"
      register: outline_install
      args:
        executable: /bin/bash

    - name: Получить конфиг Outline VPN
      ansible.builtin.command: "sudo cat /opt/outline/access.txt"
      register: outline_config
      changed_when: false

    - name: Парсинг конфига Outline VPN
      ansible.builtin.set_fact:
        outline_dict: >-
          {% set result = {} %}
          {% for line in outline_config.stdout_lines %}
            {% if ':' in line %}
              {% set key, value = line.split(':', 1) %}
              {% set _ = result.update({key.strip(): value.strip()}) %}
            {% endif %}
          {% endfor %}
          {{ result }}

    - name: Конвертировать в JSON
      ansible.builtin.set_fact:
        final_json: "{{ outline_dict | to_json }}"

    - name: Вывести конфиг
      ansible.builtin.debug:
        var: outline_dict
        verbosity: 0
