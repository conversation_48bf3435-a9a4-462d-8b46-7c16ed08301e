---
- name: Простое исправление 3x-ui базы данных
  hosts: webservers
  become: true
  vars:
    panel_port: 8443
    panel_username: "secure_admin"
    panel_password: "MySecurePass123!"
    x_ui_db_path: "/etc/x-ui/x-ui.db"
    cert_dir: "/root/cert"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Сгенерировать случайный UUID v4 для webBasePath
      shell: python3 -c "import uuid; print('/' + str(uuid.uuid4()) + '/')"
      register: uuid_result
      changed_when: false

    - name: Установить webBasePath с UUID v4
      set_fact:
        web_base_path: "{{ uuid_result.stdout }}"

    - name: Остановить x-ui
      systemd:
        name: x-ui
        state: stopped

    - name: Создать резервную копию БД
      copy:
        src: "{{ x_ui_db_path }}"
        dest: "{{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}"
        remote_src: yes

    - name: Показать текущие дублированные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} "SELECT key, COUNT(*) FROM settings GROUP BY key HAVING COUNT(*) > 1;"
      register: duplicates_before
      changed_when: false

    - name: Вывести дублированные настройки
      debug:
        msg: "Дублированные настройки до исправления: {{ duplicates_before.stdout_lines }}"

    - name: Удалить дублированные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        DELETE FROM settings WHERE id NOT IN (
          SELECT MAX(id) FROM settings GROUP BY key
        );
        EOF

    - name: Получить MD5 хеш пароля
      shell: echo -n "{{ panel_password }}" | md5sum | cut -d' ' -f1
      register: password_hash

    - name: Обновить настройки панели
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        DELETE FROM settings WHERE key IN ('webPort', 'webCertFile', 'webKeyFile', 'webListen', 'webBasePath');
        INSERT INTO settings (key, value) VALUES ('webPort', '{{ panel_port }}');
        INSERT INTO settings (key, value) VALUES ('webCertFile', '{{ cert_dir }}/cert.crt');
        INSERT INTO settings (key, value) VALUES ('webKeyFile', '{{ cert_dir }}/secret.key');
        INSERT INTO settings (key, value) VALUES ('webListen', '');
        INSERT INTO settings (key, value) VALUES ('webBasePath', '{{ web_base_path }}');
        UPDATE users SET username = '{{ panel_username }}', password = '{{ password_hash.stdout }}' WHERE id = 1;
        EOF

    - name: Проверить исправленные настройки
      shell: |
        sqlite3 {{ x_ui_db_path }} "SELECT key, value FROM settings WHERE key LIKE 'web%' ORDER BY key;"
      register: fixed_settings
      changed_when: false

    - name: Показать исправленные настройки
      debug:
        msg: "{{ fixed_settings.stdout_lines }}"

    - name: Проверить пользователя
      shell: |
        sqlite3 {{ x_ui_db_path }} "SELECT id, username FROM users WHERE id = 1;"
      register: fixed_user
      changed_when: false

    - name: Показать обновленного пользователя
      debug:
        msg: "{{ fixed_user.stdout_lines }}"

    - name: Проверить дублированные настройки после исправления
      shell: |
        sqlite3 {{ x_ui_db_path }} "SELECT key, COUNT(*) FROM settings GROUP BY key HAVING COUNT(*) > 1;"
      register: duplicates_after
      changed_when: false

    - name: Показать результат проверки дублей
      debug:
        msg: "Дублированные настройки после исправления: {{ duplicates_after.stdout_lines if duplicates_after.stdout_lines else ['Дублей нет'] }}"

    - name: Запустить x-ui
      systemd:
        name: x-ui
        state: started
        enabled: yes

    - name: Подождать запуска панели
      wait_for:
        port: "{{ panel_port }}"
        host: "0.0.0.0"
        delay: 5
        timeout: 30

    - name: Проверить доступность панели
      uri:
        url: "https://{{ server_ip }}:{{ panel_port }}{{ web_base_path }}"
        method: GET
        validate_certs: no
        timeout: 10
      register: panel_check
      ignore_errors: yes

    - name: Создать отчет об исправлении
      copy:
        content: |
          ==========================================
          ✅ 3X-UI БАЗА ДАННЫХ ИСПРАВЛЕНА
          ==========================================

          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 IP: {{ server_ip }}

          ==========================================
          🔧 НОВЫЕ НАСТРОЙКИ
          ==========================================

          🔗 URL: https://{{ server_ip }}:{{ panel_port }}{{ web_base_path }}
          👤 Логин: {{ panel_username }}
          🔑 Пароль: {{ panel_password }}
          🔧 Порт: {{ panel_port }}
          🛡️ Базовый путь: {{ web_base_path }}

          ==========================================
          📊 ИСПРАВЛЕННЫЕ НАСТРОЙКИ
          ==========================================

          {{ fixed_settings.stdout }}

          ==========================================
          👥 ПОЛЬЗОВАТЕЛЬ
          ==========================================

          {{ fixed_user.stdout }}

          ==========================================
          ✅ РЕЗУЛЬТАТ ИСПРАВЛЕНИЯ
          ==========================================

          Дублированные настройки до: {{ duplicates_before.stdout if duplicates_before.stdout else 'Нет' }}
          Дублированные настройки после: {{ duplicates_after.stdout if duplicates_after.stdout else 'Нет' }}

          Панель доступна: {{ 'Да' if panel_check.status == 200 else 'Нет (проверьте фаервол)' }}

          Резервная копия: {{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}

          ==========================================
          🚀 СЛЕДУЮЩИЕ ШАГИ
          ==========================================

          1. Откройте: https://{{ server_ip }}:{{ panel_port }}{{ web_base_path }}
          2. Войдите: {{ panel_username }} / {{ panel_password }}
          3. Настройте фаервол: ufw allow {{ panel_port }}/tcp
          4. Проверьте SSL настройки в панели

          ==========================================
        dest: "{{ cert_dir }}/FIX_REPORT.txt"
        mode: "0600"

    - name: Показать финальный результат
      debug:
        msg:
          - "✅ Исправление завершено!"
          - ""
          - "🔗 Панель: https://{{ server_ip }}:{{ panel_port }}{{ web_base_path }}"
          - "👤 Логин: {{ panel_username }}"
          - "🔑 Пароль: {{ panel_password }}"
          - "🛡️ Базовый путь: {{ web_base_path }}"
          - ""
          - "🔧 Дублированные настройки: {{ 'Исправлены' if not duplicates_after.stdout_lines else 'Остались проблемы' }}"
          - "🌐 Панель: {{ 'Доступна' if panel_check.status == 200 else 'Недоступна' }}"
          - ""
          - "📋 Отчет: {{ cert_dir }}/FIX_REPORT.txt"
