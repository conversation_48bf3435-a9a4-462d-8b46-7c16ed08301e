---
- name: Настроить SSL и безопасность для существующей 3x-ui панели
  hosts: webservers
  become: true
  vars:
    # Настройки (можно изменить)
    panel_port: 8443
    panel_username: "secure_admin"
    panel_password: "{{ lookup('password', '/tmp/3x_ui_pass chars=ascii_letters,digits,punctuation length=20') }}"
    
    # Пути к сертификатам
    cert_dir: "/root/cert"
    ssl_cert_file: "{{ cert_dir }}/cert.crt"
    ssl_key_file: "{{ cert_dir }}/secret.key"
    
    # База данных
    x_ui_db_path: "/etc/x-ui/x-ui.db"

  tasks:
    - name: Проверить, что 3x-ui установлен
      stat:
        path: "{{ x_ui_db_path }}"
      register: x_ui_installed

    - name: Завершить, если 3x-ui не установлен
      fail:
        msg: "3x-ui не установлен! Сначала запустите install_3x_ui.yml"
      when: not x_ui_installed.stat.exists

    - name: Проверить существование SSL сертификатов
      stat:
        path: "{{ item }}"
      register: cert_check
      loop:
        - "{{ ssl_cert_file }}"
        - "{{ ssl_key_file }}"

    - name: Завершить, если сертификаты не найдены
      fail:
        msg: "SSL сертификаты не найдены! Сначала запустите create_ssl_cert_manual.yml"
      when: not item.stat.exists
      loop: "{{ cert_check.results }}"

    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Остановить x-ui для безопасного изменения настроек
      systemd:
        name: x-ui
        state: stopped

    - name: Создать резервную копию базы данных
      copy:
        src: "{{ x_ui_db_path }}"
        dest: "{{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}"
        remote_src: yes

    - name: Получить MD5 хеш пароля
      shell: |
        echo -n "{{ panel_password }}" | md5sum | cut -d' ' -f1
      register: password_hash

    - name: Обновить настройки панели
      shell: |
        sqlite3 {{ x_ui_db_path }} << 'EOF'
        -- Обновить порт панели
        INSERT OR REPLACE INTO settings (key, value) VALUES ('webPort', '{{ panel_port }}');
        
        -- Настроить SSL сертификаты
        INSERT OR REPLACE INTO settings (key, value) VALUES ('webCertFile', '{{ ssl_cert_file }}');
        INSERT OR REPLACE INTO settings (key, value) VALUES ('webKeyFile', '{{ ssl_key_file }}');
        
        -- Настроить адрес прослушивания (пустой = все интерфейсы)
        INSERT OR REPLACE INTO settings (key, value) VALUES ('webListen', '');
        
        -- Базовый путь панели
        INSERT OR REPLACE INTO settings (key, value) VALUES ('webBasePath', '/');
        
        -- Обновить учетные данные администратора
        UPDATE users SET username = '{{ panel_username }}', password = '{{ password_hash.stdout }}' WHERE id = 1;
        
        .quit
        EOF

    - name: Запустить x-ui с новыми настройками
      systemd:
        name: x-ui
        state: started
        enabled: yes

    - name: Подождать запуска панели
      wait_for:
        port: "{{ panel_port }}"
        host: "0.0.0.0"
        delay: 3
        timeout: 30

    - name: Проверить доступность панели
      uri:
        url: "https://{{ server_ip }}:{{ panel_port }}"
        method: GET
        validate_certs: no
        timeout: 10
      register: panel_check
      ignore_errors: yes

    - name: Создать отчет о настройке
      copy:
        content: |
          ==========================================
          ✅ 3X-UI ПАНЕЛЬ НАСТРОЕНА С SSL
          ==========================================
          
          🌐 IP адрес: {{ server_ip }}
          🔗 HTTPS URL: https://{{ server_ip }}:{{ panel_port }}
          👤 Логин: {{ panel_username }}
          🔑 Пароль: {{ panel_password }}
          
          ==========================================
          🔐 SSL СЕРТИФИКАТЫ
          ==========================================
          
          📁 Директория: {{ cert_dir }}
          🔐 Сертификат: {{ ssl_cert_file }}
          🔑 Ключ: {{ ssl_key_file }}
          
          ==========================================
          📋 ТЕХНИЧЕСКАЯ ИНФОРМАЦИЯ
          ==========================================
          
          🔧 Порт панели: {{ panel_port }}
          💾 База данных: {{ x_ui_db_path }}
          💾 Резервная копия: {{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }}
          🔒 Хеш пароля: {{ password_hash.stdout }}
          
          ==========================================
          ⚠️  БЕЗОПАСНОСТЬ
          ==========================================
          
          • Стандартные admin/admin ОТКЛЮЧЕНЫ
          • SSL сертификаты активированы
          • Порт изменен с 2053 на {{ panel_port }}
          • Сохраните эти данные в безопасном месте!
          
          ==========================================
          🚀 СЛЕДУЮЩИЕ ШАГИ
          ==========================================
          
          1. Откройте: https://{{ server_ip }}:{{ panel_port }}
          2. Войдите с новыми учетными данными
          3. Настройте фаервол для порта {{ panel_port }}:
             ufw allow {{ panel_port }}/tcp
          4. Создайте пользователей VPN
          
          ==========================================
          🔄 ВОССТАНОВЛЕНИЕ
          ==========================================
          
          Для сброса настроек:
          1. systemctl stop x-ui
          2. cp {{ x_ui_db_path }}.backup.{{ ansible_date_time.epoch }} {{ x_ui_db_path }}
          3. systemctl start x-ui
          
          Или используйте команду: x-ui
          
          ==========================================
        dest: "{{ cert_dir }}/SSL_SETUP_REPORT.txt"
        mode: "0600"

    - name: Показать результат настройки
      debug:
        msg:
          - "✅ Настройка SSL завершена успешно!"
          - ""
          - "🔗 Панель: https://{{ server_ip }}:{{ panel_port }}"
          - "👤 Логин: {{ panel_username }}"
          - "🔑 Пароль: {{ panel_password }}"
          - ""
          - "📋 Отчет: {{ cert_dir }}/SSL_SETUP_REPORT.txt"
          - ""
          - "⚠️  Настройте фаервол: ufw allow {{ panel_port }}/tcp"

    - name: Показать статус панели
      debug:
        msg: "Панель {{ 'доступна' if panel_check.status == 200 else 'может быть недоступна (проверьте фаервол)' }}"
