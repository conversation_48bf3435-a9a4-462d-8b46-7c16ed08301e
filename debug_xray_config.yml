---
- name: Ди<PERSON>гностика конфигурации Xray
  hosts: webservers
  become: true
  vars:
    xray_config_path: "/usr/local/x-ui/bin/config.json"
    backup_dir: "/root/cert/xray_backups"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Проверить размер и дату изменения конфигурационного файла
      stat:
        path: "{{ xray_config_path }}"
      register: config_file_stat

    - name: Показать информацию о файле конфигурации
      debug:
        msg:
          - "📁 Файл конфигурации: {{ xray_config_path }}"
          - "📏 Размер: {{ config_file_stat.stat.size }} байт"
          - "📅 Последнее изменение: {{ config_file_stat.stat.mtime | int | to_datetime }}"
          - "👤 Владелец: {{ config_file_stat.stat.pw_name }}"
          - "🔒 Права: {{ config_file_stat.stat.mode }}"

    - name: Найти все резервные копии
      find:
        paths: "{{ backup_dir }}"
        patterns: "config.json.backup.*"
        file_type: file
      register: backup_files

    - name: Показать информацию о резервных копиях
      debug:
        msg:
          - "💾 Найдено резервных копий: {{ backup_files.files | length }}"
          - "{% for backup in backup_files.files | sort(attribute='mtime', reverse=true) %}"
          - "  • {{ backup.path | basename }}: {{ backup.mtime | int | to_datetime }} ({{ backup.size }} байт)"
          - "{% endfor %}"

    - name: Сравнить размеры текущего файла и последней резервной копии
      set_fact:
        latest_backup: "{{ backup_files.files | sort(attribute='mtime', reverse=true) | first }}"
      when: backup_files.files | length > 0

    - name: Показать сравнение размеров
      debug:
        msg:
          - "📊 Сравнение размеров:"
          - "  Текущий файл: {{ config_file_stat.stat.size }} байт"
          - "  Последняя резервная копия: {{ latest_backup.size | default('N/A') }} байт"
          - "  {% if latest_backup is defined %}Разница: {{ config_file_stat.stat.size - latest_backup.size }} байт{% endif %}"
      when: backup_files.files | length > 0

    - name: Прочитать текущую конфигурацию
      slurp:
        src: "{{ xray_config_path }}"
      register: current_config_raw

    - name: Прочитать последнюю резервную копию
      slurp:
        src: "{{ latest_backup.path }}"
      register: backup_config_raw
      when: backup_files.files | length > 0

    - name: Парсинг конфигураций
      set_fact:
        current_config: "{{ current_config_raw.content | b64decode | from_json }}"
        backup_config: "{{ backup_config_raw.content | b64decode | from_json if backup_files.files | length > 0 else {} }}"

    - name: Сравнить количество правил
      debug:
        msg:
          - "📈 Сравнение правил маршрутизации:"
          - "  Текущая конфигурация: {{ current_config.routing.rules | default([]) | length }} правил"
          - "  {% if backup_config %}Резервная копия: {{ backup_config.routing.rules | default([]) | length }} правил{% endif %}"

    - name: Показать текущие правила маршрутизации
      debug:
        msg:
          - "📋 ТЕКУЩИЕ ПРАВИЛА МАРШРУТИЗАЦИИ:"
          - "{% for rule in current_config.routing.rules | default([]) %}"
          - "  [{{ loop.index0 }}] {{ rule.type | default('field') }} → {{ rule.outboundTag | default('default') }}"
          - "    {% if rule.domain is defined %}Домены: {{ rule.domain | join(', ') }}{% endif %}"
          - "    {% if rule.ip is defined %}IP: {{ rule.ip | join(', ') }}{% endif %}"
          - "    {% if rule.inboundTag is defined %}InboundTag: {{ rule.inboundTag | join(', ') }}{% endif %}"
          - "    {% if rule.protocol is defined %}Протоколы: {{ rule.protocol | join(', ') }}{% endif %}"
          - "{% endfor %}"

    - name: Показать правила из резервной копии
      debug:
        msg:
          - "💾 ПРАВИЛА ИЗ РЕЗЕРВНОЙ КОПИИ:"
          - "{% for rule in backup_config.routing.rules | default([]) %}"
          - "  [{{ loop.index0 }}] {{ rule.type | default('field') }} → {{ rule.outboundTag | default('default') }}"
          - "    {% if rule.domain is defined %}Домены: {{ rule.domain | join(', ') }}{% endif %}"
          - "    {% if rule.ip is defined %}IP: {{ rule.ip | join(', ') }}{% endif %}"
          - "    {% if rule.inboundTag is defined %}InboundTag: {{ rule.inboundTag | join(', ') }}{% endif %}"
          - "    {% if rule.protocol is defined %}Протоколы: {{ rule.protocol | join(', ') }}{% endif %}"
          - "{% endfor %}"
      when: backup_config

    - name: Проверить процессы x-ui
      shell: |
        echo "=== ПРОЦЕССЫ X-UI ==="
        ps aux | grep x-ui | grep -v grep || echo "Процессы x-ui не найдены"
        echo ""
        echo "=== СТАТУС СЕРВИСА ==="
        systemctl status x-ui --no-pager -l || echo "Ошибка получения статуса"
        echo ""
        echo "=== ПОСЛЕДНИЕ ЛОГИ ==="
        journalctl -u x-ui --no-pager -n 10 || echo "Ошибка получения логов"
      register: x_ui_processes
      changed_when: false

    - name: Показать информацию о процессах и логах
      debug:
        msg: "{{ x_ui_processes.stdout_lines }}"

    - name: Проверить права доступа к конфигурационному файлу
      shell: |
        echo "=== ПРАВА ДОСТУПА ==="
        ls -la {{ xray_config_path }}
        echo ""
        echo "=== ДИРЕКТОРИЯ ==="
        ls -la {{ xray_config_path | dirname }}
        echo ""
        echo "=== ВЛАДЕЛЕЦ ПРОЦЕССА X-UI ==="
        ps -eo pid,user,cmd | grep x-ui | grep -v grep || echo "Процесс не найден"
      register: file_permissions
      changed_when: false

    - name: Показать права доступа
      debug:
        msg: "{{ file_permissions.stdout_lines }}"

    - name: Проверить есть ли другие конфигурационные файлы
      find:
        paths:
          - /usr/local/x-ui
          - /etc/x-ui
        patterns: "*.json"
        recurse: yes
      register: other_configs

    - name: Показать другие конфигурационные файлы
      debug:
        msg:
          - "🔍 Другие JSON файлы в системе:"
          - "{% for file in other_configs.files %}"
          - "  • {{ file.path }} ({{ file.size }} байт, {{ file.mtime | int | to_datetime }})"
          - "{% endfor %}"

    - name: Создать диагностический отчет
      copy:
        content: |
          ==========================================
          🔍 ДИАГНОСТИЧЕСКИЙ ОТЧЕТ XRAY
          ==========================================
          
          📅 Дата диагностики: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          
          ==========================================
          📁 ИНФОРМАЦИЯ О ФАЙЛЕ КОНФИГУРАЦИИ
          ==========================================
          
          Путь: {{ xray_config_path }}
          Размер: {{ config_file_stat.stat.size }} байт
          Последнее изменение: {{ config_file_stat.stat.mtime | int | to_datetime }}
          Владелец: {{ config_file_stat.stat.pw_name }}
          Права: {{ config_file_stat.stat.mode }}
          
          ==========================================
          💾 РЕЗЕРВНЫЕ КОПИИ
          ==========================================
          
          Количество резервных копий: {{ backup_files.files | length }}
          {% if latest_backup is defined %}
          Последняя резервная копия:
          - Файл: {{ latest_backup.path }}
          - Размер: {{ latest_backup.size }} байт
          - Дата: {{ latest_backup.mtime | int | to_datetime }}
          - Разница в размере: {{ config_file_stat.stat.size - latest_backup.size }} байт
          {% endif %}
          
          ==========================================
          📊 СРАВНЕНИЕ ПРАВИЛ
          ==========================================
          
          Текущая конфигурация: {{ current_config.routing.rules | default([]) | length }} правил
          {% if backup_config %}Резервная копия: {{ backup_config.routing.rules | default([]) | length }} правил{% endif %}
          
          ==========================================
          📋 ТЕКУЩИЕ ПРАВИЛА
          ==========================================
          
          {% for rule in current_config.routing.rules | default([]) %}
          [{{ loop.index0 }}] {{ rule.type | default('field') }} → {{ rule.outboundTag | default('default') }}
          {% if rule.domain is defined %}  Домены: {{ rule.domain | join(', ') }}{% endif %}
          {% if rule.ip is defined %}  IP: {{ rule.ip | join(', ') }}{% endif %}
          {% if rule.inboundTag is defined %}  InboundTag: {{ rule.inboundTag | join(', ') }}{% endif %}
          {% if rule.protocol is defined %}  Протоколы: {{ rule.protocol | join(', ') }}{% endif %}
          
          {% endfor %}
          ==========================================
          🔧 ПРОЦЕССЫ И ЛОГИ
          ==========================================
          
          {{ x_ui_processes.stdout }}
          
          ==========================================
          🔒 ПРАВА ДОСТУПА
          ==========================================
          
          {{ file_permissions.stdout }}
          
          ==========================================
          📁 ДРУГИЕ КОНФИГУРАЦИОННЫЕ ФАЙЛЫ
          ==========================================
          
          {% for file in other_configs.files %}
          {{ file.path }} ({{ file.size }} байт)
          {% endfor %}
          
          ==========================================
          💡 ВОЗМОЖНЫЕ ПРИЧИНЫ ПРОБЛЕМЫ
          ==========================================
          
          1. X-UI перезаписал конфигурацию при перезапуске
          2. Конфигурация сохраняется в другом месте
          3. X-UI использует базу данных для генерации конфигурации
          4. Права доступа препятствуют сохранению изменений
          
          ==========================================
        dest: "{{ backup_dir }}/DIAGNOSTIC_REPORT.txt"
        mode: "0644"

    - name: Показать заключение диагностики
      debug:
        msg:
          - "🔍 Диагностика завершена"
          - ""
          - "📊 Основные выводы:"
          - "  • Текущий размер конфигурации: {{ config_file_stat.stat.size }} байт"
          - "  • Количество правил: {{ current_config.routing.rules | default([]) | length }}"
          - "  • {% if latest_backup is defined %}Изменение размера: {{ config_file_stat.stat.size - latest_backup.size }} байт{% endif %}"
          - ""
          - "🔧 Возможные причины:"
          - "  • X-UI может перезаписывать конфигурацию при перезапуске"
          - "  • Конфигурация может генерироваться из базы данных"
          - "  • Нужно проверить настройки в веб-интерфейсе X-UI"
          - ""
          - "📋 Отчет сохранен: {{ backup_dir }}/DIAGNOSTIC_REPORT.txt"
