- name: Создание самоподписанного сертификата
  hosts: webservers
  become: true
  vars:
    cert_dir: /root/cert
    key_file: secret.key
    csr_file: cert.csr
    crt_file: cert.crt
    cert_days: 365
    common_name: "{{ ansible_host }}" # Можно задать IP или домен вручную

  tasks:
    - name: Создать директорию для сертификатов
      file:
        path: "{{ cert_dir }}"
        state: directory
        mode: "0700"

    - name: Создать закрытый ключ secret.key
      openssl_privatekey:
        path: "{{ cert_dir }}/{{ key_file }}"
        size: 2048
        state: present
        mode: "0600"

    - name: Создать запрос на подпись сертификата (CSR)
      openssl_csr:
        path: "{{ cert_dir }}/{{ csr_file }}"
        privatekey_path: "{{ cert_dir }}/{{ key_file }}"
        common_name: "{{ common_name }}"
        country_name: "RU"
        state_or_province_name: "SomeState"
        locality_name: "SomeCity"
        organization_name: "MyOrganization"
        organizational_unit_name: "IT"
        email_address: "<EMAIL>"
        mode: "0644"

    - name: Создать самоподписанный сертификат cert.crt
      community.crypto.openssl_certificate:
        path: "{{ cert_dir }}/{{ crt_file }}"
        privatekey_path: "{{ cert_dir }}/{{ key_file }}"
        csr_path: "{{ cert_dir }}/{{ csr_file }}"
        provider: selfsigned
        selfsigned_not_after: "+365d"
        mode: "0644"

    - name: Показать пути к сертификатам
      debug:
        msg:
          - "Путь к сертификату: {{ cert_dir }}/{{ crt_file }}"
          - "Путь к ключу: {{ cert_dir }}/{{ key_file }}"
