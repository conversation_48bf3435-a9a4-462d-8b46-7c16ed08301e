---
- name: Установить 3x-ui панель с SSL сертификатами
  hosts: webservers
  become: true
  vars:
    cert_dir: "/root/cert"
    cert_validity_days: 365

  tasks:
    # Установка 3x-ui панели
    - name: Обновить пакеты и установить зависимости
      apt:
        update_cache: yes
        upgrade: dist

    - name: Установить необходимые пакеты
      apt:
        name:
          - curl
          - wget
          - unzip
          - openssl
        state: present

    - name: Установить 3x-ui панель управления без интерактива
      shell: |
        echo "n" | bash <(curl -Ls https://raw.githubusercontent.com/mhsanaei/3x-ui/master/install.sh)
      args:
        executable: /bin/bash

    # Создание SSL сертификатов
    - name: Создать директорию для сертификатов
      file:
        path: "{{ cert_dir }}"
        state: directory
        mode: '0700'
        owner: root
        group: root

    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать закрытый ключ (2048 бит)
      command: openssl genrsa -out secret.key 2048
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/secret.key"

    - name: Установить права на закрытый ключ
      file:
        path: "{{ cert_dir }}/secret.key"
        mode: '0600'
        owner: root
        group: root

    - name: Создать конфигурационный файл для CSR
      copy:
        content: |
          [req]
          distinguished_name = req_distinguished_name
          req_extensions = v3_req
          prompt = no

          [req_distinguished_name]
          C = RU
          ST = Moscow
          L = Moscow
          O = Private
          OU = IT
          CN = {{ server_ip }}
          emailAddress = admin@{{ server_ip }}

          [v3_req]
          keyUsage = keyEncipherment, dataEncipherment
          extendedKeyUsage = serverAuth
          subjectAltName = @alt_names

          [alt_names]
          IP.1 = {{ server_ip }}
        dest: "{{ cert_dir }}/openssl.conf"
        mode: '0644'

    - name: Создать запрос на подпись сертификата (CSR)
      command: openssl req -key secret.key -new -out cert.csr -config openssl.conf
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/cert.csr"

    - name: Создать самоподписанный сертификат
      command: openssl x509 -signkey secret.key -in cert.csr -req -days {{ cert_validity_days }} -out cert.crt -extensions v3_req -extfile openssl.conf
      args:
        chdir: "{{ cert_dir }}"
        creates: "{{ cert_dir }}/cert.crt"

    - name: Установить права на сертификат
      file:
        path: "{{ cert_dir }}/cert.crt"
        mode: '0644'
        owner: root
        group: root

    - name: Создать файл с инструкциями по настройке SSL
      copy:
        content: |
          ===========================================
          3X-UI ПАНЕЛЬ УСТАНОВЛЕНА С SSL СЕРТИФИКАТАМИ
          ===========================================
          
          Пути к файлам для настройки SSL в 3x-ui панели:
          
          Путь к файлу публичного ключа сертификата панели:
          {{ cert_dir }}/cert.crt
          
          Путь к файлу приватного ключа сертификата панели:
          {{ cert_dir }}/secret.key
          
          IP адрес сервера: {{ server_ip }}
          Срок действия сертификата: {{ cert_validity_days }} дней
          
          ===========================================
          СЛЕДУЮЩИЕ ШАГИ:
          ===========================================
          
          1. Откройте веб-интерфейс 3x-ui панели по адресу: http://{{ server_ip }}:2053
          2. Войдите в систему (логин/пароль по умолчанию: admin/admin)
          3. Перейдите в настройки панели
          4. Включите SSL и укажите пути к сертификатам:
             - Публичный ключ: {{ cert_dir }}/cert.crt
             - Приватный ключ: {{ cert_dir }}/secret.key
          5. Сохраните настройки и перезапустите панель
          6. После этого панель будет доступна по HTTPS: https://{{ server_ip }}:2053
          7. Также добавьте эти ключи во вкладку "Подписка"
          
          ===========================================
        dest: "{{ cert_dir }}/SSL_SETUP_GUIDE.txt"
        mode: '0644'

    - name: Показать финальную информацию
      debug:
        msg:
          - "🎉 3x-ui панель успешно установлена!"
          - "🔐 SSL сертификаты созданы!"
          - "🌐 IP адрес сервера: {{ server_ip }}"
          - "📋 Веб-интерфейс: http://{{ server_ip }}:2053"
          - "📁 Сертификаты находятся в: {{ cert_dir }}"
          - "📖 Инструкция: {{ cert_dir }}/SSL_SETUP_GUIDE.txt"
          - ""
          - "Следующие шаги:"
          - "1. Откройте веб-интерфейс панели"
          - "2. Настройте SSL с созданными сертификатами"
          - "3. Перезапустите панель для применения SSL"
