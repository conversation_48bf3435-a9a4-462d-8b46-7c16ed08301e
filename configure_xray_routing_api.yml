---
- name: Настроить маршрутизацию Xray через API 3x-ui панели
  hosts: webservers
  become: true
  vars:
    # Учетные данные для API 3x-ui
    panel_url: "http://************:23456/IxIPY9iNxOOpf9Zb6n"
    panel_username: "fSkACNeS"
    panel_password: "imGecmwscOT7Pm"
    
    # Директория для отчетов
    backup_dir: "/root/cert/xray_backups"
    
    # Правила маршрутизации для добавления
    routing_rules:
      - type: "field"
        domain:
          - "geosite:category-gov-ru"
        outboundTag: "direct"
        comment: "Russian government sites"
      - type: "field"
        domain:
          - "regexp:.*\\.ru$"
        outboundTag: "direct"
        comment: "All .ru domains"
      - type: "field"
        domain:
          - "geosite:openai"
        outboundTag: "direct"
        comment: "OpenAI services"
      - type: "field"
        ip:
          - "geoip:ru"
        outboundTag: "direct"
        comment: "Russian IP addresses"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать директорию для отчетов
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: "0700"

    - name: Проверить доступность панели 3x-ui
      uri:
        url: "{{ panel_url }}"
        method: GET
        timeout: 10
        status_code: [200, 302, 401]
      register: panel_availability
      ignore_errors: yes

    - name: Завершить если панель недоступна
      fail:
        msg: "Панель 3x-ui недоступна по адресу {{ panel_url }}"
      when: panel_availability.status is not defined or panel_availability.status not in [200, 302, 401]

    - name: Показать статус доступности панели
      debug:
        msg:
          - "🌐 Панель 3x-ui доступна"
          - "📡 URL: {{ panel_url }}"
          - "📊 HTTP статус: {{ panel_availability.status }}"

    - name: Аутентификация в панели 3x-ui
      uri:
        url: "{{ panel_url }}/login"
        method: POST
        body_format: form-urlencoded
        body:
          username: "{{ panel_username }}"
          password: "{{ panel_password }}"
        timeout: 30
        status_code: [200, 302]
        follow_redirects: no
      register: login_response
      ignore_errors: yes

    - name: Проверить успешность аутентификации
      debug:
        msg:
          - "🔐 Результат аутентификации:"
          - "📊 HTTP статус: {{ login_response.status | default('Ошибка') }}"
          - "🍪 Cookies: {{ 'Получены' if login_response.cookies is defined else 'Отсутствуют' }}"

    - name: Завершить если аутентификация не удалась
      fail:
        msg: "Ошибка аутентификации в панели 3x-ui"
      when: login_response.status is not defined or login_response.status not in [200, 302]

    - name: Получить текущую конфигурацию через API
      uri:
        url: "{{ panel_url }}/panel/api/xray/config"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200]
      register: current_config_response
      ignore_errors: yes

    - name: Показать результат получения конфигурации
      debug:
        msg:
          - "📋 Получение конфигурации:"
          - "📊 HTTP статус: {{ current_config_response.status | default('Ошибка') }}"
          - "📏 Размер ответа: {{ current_config_response.content | default('') | length }} символов"

    - name: Парсинг текущей конфигурации
      set_fact:
        current_xray_config: "{{ current_config_response.json | default({}) }}"
      when: current_config_response.status is defined and current_config_response.status == 200

    - name: Показать текущие правила маршрутизации
      debug:
        msg:
          - "🛣️ Текущие правила маршрутизации:"
          - "📊 Всего правил: {{ current_xray_config.routing.rules | default([]) | length }}"
          - "🎯 Direct правил: {{ current_xray_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'direct') | list | length }}"

    - name: Создать Python скрипт для работы с API
      copy:
        content: |
          #!/usr/bin/env python3
          import json
          import requests
          import sys
          from urllib.parse import urljoin
          
          def login_to_panel(base_url, username, password):
              """Аутентификация в панели 3x-ui"""
              login_url = urljoin(base_url, '/login')
              
              session = requests.Session()
              
              # Попробуем разные методы аутентификации
              login_data = {
                  'username': username,
                  'password': password
              }
              
              try:
                  response = session.post(login_url, data=login_data, timeout=30, allow_redirects=False)
                  print(f"Login response status: {response.status_code}")
                  
                  if response.status_code in [200, 302]:
                      return session
                  else:
                      print(f"Login failed with status: {response.status_code}")
                      return None
              except Exception as e:
                  print(f"Login error: {e}")
                  return None
          
          def get_xray_config(session, base_url):
              """Получить текущую конфигурацию Xray"""
              config_url = urljoin(base_url, '/panel/api/xray/config')
              
              try:
                  response = session.get(config_url, timeout=30)
                  print(f"Get config response status: {response.status_code}")
                  
                  if response.status_code == 200:
                      return response.json()
                  else:
                      print(f"Failed to get config: {response.status_code}")
                      return None
              except Exception as e:
                  print(f"Get config error: {e}")
                  return None
          
          def update_xray_config(session, base_url, config):
              """Обновить конфигурацию Xray"""
              update_url = urljoin(base_url, '/panel/api/xray/config')
              
              try:
                  headers = {'Content-Type': 'application/json'}
                  response = session.post(update_url, json=config, headers=headers, timeout=30)
                  print(f"Update config response status: {response.status_code}")
                  
                  if response.status_code == 200:
                      return True
                  else:
                      print(f"Failed to update config: {response.status_code}")
                      print(f"Response: {response.text}")
                      return False
              except Exception as e:
                  print(f"Update config error: {e}")
                  return False
          
          def add_routing_rules(config, new_rules):
              """Добавить новые правила маршрутизации"""
              if 'routing' not in config:
                  config['routing'] = {'rules': []}
              
              if 'rules' not in config['routing']:
                  config['routing']['rules'] = []
              
              existing_rules = config['routing']['rules']
              added_count = 0
              
              for rule in new_rules:
                  # Проверяем, существует ли уже такое правило
                  rule_exists = False
                  for existing_rule in existing_rules:
                      if ('domain' in rule and 'domain' in existing_rule and 
                          set(rule['domain']) == set(existing_rule['domain'])):
                          rule_exists = True
                          break
                      if ('ip' in rule and 'ip' in existing_rule and 
                          set(rule['ip']) == set(existing_rule['ip'])):
                          rule_exists = True
                          break
                  
                  if not rule_exists:
                      # Удаляем comment перед добавлением
                      rule_to_add = {k: v for k, v in rule.items() if k != 'comment'}
                      existing_rules.append(rule_to_add)
                      added_count += 1
                      print(f"Added rule: {rule.get('comment', 'No description')}")
                  else:
                      print(f"Rule already exists: {rule.get('comment', 'No description')}")
              
              return config, added_count
          
          if __name__ == "__main__":
              if len(sys.argv) != 5:
                  print("Usage: script.py <base_url> <username> <password> <rules_file>")
                  sys.exit(1)
              
              base_url = sys.argv[1]
              username = sys.argv[2]
              password = sys.argv[3]
              rules_file = sys.argv[4]
              
              # Читаем новые правила
              with open(rules_file, 'r', encoding='utf-8') as f:
                  new_rules = json.load(f)
              
              # Аутентификация
              session = login_to_panel(base_url, username, password)
              if not session:
                  print("Authentication failed")
                  sys.exit(1)
              
              # Получаем текущую конфигурацию
              config = get_xray_config(session, base_url)
              if not config:
                  print("Failed to get current configuration")
                  sys.exit(1)
              
              print(f"Current rules count: {len(config.get('routing', {}).get('rules', []))}")
              
              # Добавляем новые правила
              updated_config, added_count = add_routing_rules(config, new_rules)
              
              print(f"Added {added_count} new rules")
              print(f"Total rules after update: {len(updated_config.get('routing', {}).get('rules', []))}")
              
              # Обновляем конфигурацию
              if update_xray_config(session, base_url, updated_config):
                  print("Configuration updated successfully")
              else:
                  print("Failed to update configuration")
                  sys.exit(1)
        dest: /tmp/xray_api_manager.py
        mode: "0755"

    - name: Создать файл с новыми правилами для API
      copy:
        content: "{{ routing_rules | to_nice_json }}"
        dest: /tmp/api_routing_rules.json
        mode: "0644"

    - name: Добавить правила маршрутизации через API
      command: >
        python3 /tmp/xray_api_manager.py 
        "{{ panel_url }}" 
        "{{ panel_username }}" 
        "{{ panel_password }}" 
        /tmp/api_routing_rules.json
      register: api_update_result
      ignore_errors: yes

    - name: Показать результат обновления через API
      debug:
        msg:
          - "🔄 Результат обновления через API:"
          - "{{ api_update_result.stdout_lines | default(['Нет вывода']) }}"
          - "{% if api_update_result.stderr_lines %}Ошибки: {{ api_update_result.stderr_lines }}{% endif %}"

    - name: Проверить успешность обновления
      set_fact:
        api_update_success: "{{ api_update_result.rc == 0 and 'Configuration updated successfully' in api_update_result.stdout }}"

    - name: Подождать применения изменений
      pause:
        seconds: 5
      when: api_update_success

    - name: Получить обновленную конфигурацию для проверки
      uri:
        url: "{{ panel_url }}/panel/api/xray/config"
        method: GET
        headers:
          Cookie: "{{ login_response.cookies_string | default('') }}"
        timeout: 30
        status_code: [200]
      register: updated_config_response
      ignore_errors: yes
      when: api_update_success

    - name: Парсинг обновленной конфигурации
      set_fact:
        updated_xray_config: "{{ updated_config_response.json | default({}) }}"
      when: api_update_success and updated_config_response.status is defined and updated_config_response.status == 200

    - name: Показать статистику обновленной конфигурации
      debug:
        msg:
          - "📊 Статистика обновленной конфигурации:"
          - "📋 Всего правил: {{ updated_xray_config.routing.rules | default([]) | length }}"
          - "🎯 Direct правил: {{ updated_xray_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'direct') | list | length }}"
      when: api_update_success and updated_xray_config is defined

    - name: Проверить наличие наших правил в обновленной конфигурации
      set_fact:
        our_rules_status:
          gov_ru: "{{ updated_xray_config.routing.rules | default([]) | selectattr('domain', 'defined') | selectattr('domain', 'contains', 'geosite:category-gov-ru') | list | length > 0 }}"
          ru_domains: "{{ updated_xray_config.routing.rules | default([]) | selectattr('domain', 'defined') | selectattr('domain', 'contains', 'regexp:.*\\.ru$') | list | length > 0 }}"
          openai: "{{ updated_xray_config.routing.rules | default([]) | selectattr('domain', 'defined') | selectattr('domain', 'contains', 'geosite:openai') | list | length > 0 }}"
          ru_ip: "{{ updated_xray_config.routing.rules | default([]) | selectattr('ip', 'defined') | selectattr('ip', 'contains', 'geoip:ru') | list | length > 0 }}"
      when: api_update_success and updated_xray_config is defined

    - name: Создать отчет о настройке через API
      copy:
        content: |
          ==========================================
          🌐 НАСТРОЙКА МАРШРУТИЗАЦИИ ЧЕРЕЗ API 3X-UI
          ==========================================
          
          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          📡 URL панели: {{ panel_url }}
          
          ==========================================
          🔐 АУТЕНТИФИКАЦИЯ
          ==========================================
          
          Пользователь: {{ panel_username }}
          Статус аутентификации: {{ '✅ Успешно' if login_response.status in [200, 302] else '❌ Ошибка' }}
          HTTP статус: {{ login_response.status | default('N/A') }}
          
          ==========================================
          📊 РЕЗУЛЬТАТ ОБНОВЛЕНИЯ
          ==========================================
          
          Обновление через API: {{ '✅ Успешно' if api_update_success else '❌ Ошибка' }}
          Код возврата: {{ api_update_result.rc | default('N/A') }}
          
          {% if api_update_success and updated_xray_config is defined %}
          Всего правил после обновления: {{ updated_xray_config.routing.rules | default([]) | length }}
          Direct правил: {{ updated_xray_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'direct') | list | length }}
          {% endif %}
          
          ==========================================
          ✅ ПРОВЕРКА ДОБАВЛЕННЫХ ПРАВИЛ
          ==========================================
          
          {% if api_update_success and our_rules_status is defined %}
          🏛️ geosite:category-gov-ru: {{ '✅ Добавлено' if our_rules_status.gov_ru else '❌ Отсутствует' }}
          🇷🇺 regexp:.*\.ru$: {{ '✅ Добавлено' if our_rules_status.ru_domains else '❌ Отсутствует' }}
          🤖 geosite:openai: {{ '✅ Добавлено' if our_rules_status.openai else '❌ Отсутствует' }}
          🌐 geoip:ru: {{ '✅ Добавлено' if our_rules_status.ru_ip else '❌ Отсутствует' }}
          {% else %}
          Проверка правил недоступна (ошибка обновления)
          {% endif %}
          
          ==========================================
          📋 ВЫВОД СКРИПТА API
          ==========================================
          
          {{ api_update_result.stdout | default('Нет вывода') }}
          
          {% if api_update_result.stderr %}
          Ошибки:
          {{ api_update_result.stderr }}
          {% endif %}
          
          ==========================================
          🔧 ПРЕИМУЩЕСТВА API ПОДХОДА
          ==========================================
          
          • Использует официальный интерфейс 3x-ui
          • Автоматически применяет изменения
          • Избегает конфликтов с перезаписью файлов
          • Поддерживает валидацию конфигурации
          • Интегрируется с системой управления панели
          
          ==========================================
          📖 ДОБАВЛЕННЫЕ ПРАВИЛА
          ==========================================
          
          1. 🏛️ Российские государственные сайты
             Домены: geosite:category-gov-ru → direct
          
          2. 🇷🇺 Все .ru домены  
             Домены: regexp:.*\.ru$ → direct
          
          3. 🤖 Сервисы OpenAI
             Домены: geosite:openai → direct
          
          4. 🌐 Российские IP адреса
             IP: geoip:ru → direct
          
          ==========================================
        dest: "{{ backup_dir }}/API_ROUTING_REPORT.txt"
        mode: "0644"

    - name: Удалить временные файлы
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /tmp/xray_api_manager.py
        - /tmp/api_routing_rules.json

    - name: Показать финальный результат
      debug:
        msg:
          - "🌐 Настройка маршрутизации через API завершена!"
          - ""
          - "📊 Результат:"
          - "  • Аутентификация: {{ '✅ Успешно' if login_response.status in [200, 302] else '❌ Ошибка' }}"
          - "  • Обновление API: {{ '✅ Успешно' if api_update_success else '❌ Ошибка' }}"
          - "  {% if api_update_success and updated_xray_config is defined %}• Всего правил: {{ updated_xray_config.routing.rules | default([]) | length }}{% endif %}"
          - "  {% if api_update_success and updated_xray_config is defined %}• Direct правил: {{ updated_xray_config.routing.rules | default([]) | selectattr('outboundTag', 'equalto', 'direct') | list | length }}{% endif %}"
          - ""
          - "✅ Добавленные правила (через API):"
          - "  • 🏛️ geosite:category-gov-ru → direct"
          - "  • 🇷🇺 regexp:.*\\.ru$ → direct"
          - "  • 🤖 geosite:openai → direct"
          - "  • 🌐 geoip:ru → direct"
          - ""
          - "📋 Отчет: {{ backup_dir }}/API_ROUTING_REPORT.txt"
          - "📡 Панель: {{ panel_url }}"
