---
- name: Настройка маршрутизации Xray через веб-интерфейс с Playwright
  hosts: webservers
  become: true
  vars:
    panel_url: "http://************:23456/IxIPY9iNxOOpf9Zb6n"
    panel_username: "fSkACNeS"
    panel_password: "imGecmwscOT7Pm"
    backup_dir: "/root/cert/xray_backups"
    screenshots_dir: "{{ backup_dir }}/screenshots"

    routing_rules:
      - domain: "geosite:category-gov-ru"
        outbound: "direct"
        comment: "Russian government sites"
      - domain: "regexp:.*\\.ru$"
        outbound: "direct"
        comment: "All .ru domains"
      - domain: "geosite:openai"
        outbound: "direct"
        comment: "OpenAI services"
      - ip: "geoip:ru"
        outbound: "direct"
        comment: "Russian IP addresses"

  tasks:
    - name: Получить IP адрес сервера
      set_fact:
        server_ip: "{{ ansible_default_ipv4.address }}"

    - name: Создать директории для отчетов и скриншотов
      file:
        path: "{{ item }}"
        state: directory
        mode: "0755"
      loop:
        - "{{ backup_dir }}"
        - "{{ screenshots_dir }}"

    - name: Установить системные зависимости для Playwright
      apt:
        name:
          - python3
          - python3-pip
          - python3-venv
          - libnss3
          - libatk-bridge2.0-0
          - libdrm2
          - libxkbcommon0
          - libxcomposite1
          - libxdamage1
          - libxrandr2
          - libgbm1
          - libxss1
          - libasound2t64
          - libatspi2.0-0
          - libgtk-3-0
          - libgdk-pixbuf2.0-0
          - xvfb
          - fonts-liberation
          - fonts-dejavu-core
          - ca-certificates
        state: present
        update_cache: yes

    - name: Создать виртуальное окружение Python
      command: python3 -m venv /opt/playwright-env
      args:
        creates: /opt/playwright-env

    - name: Установить Playwright и зависимости в виртуальное окружение
      pip:
        name:
          - playwright
          - asyncio
        virtualenv: /opt/playwright-env

    - name: Установить браузеры Playwright
      shell: |
        source /opt/playwright-env/bin/activate
        playwright install chromium
        playwright install-deps chromium
      args:
        creates: /opt/playwright-env/lib/python*/site-packages/playwright/driver/package/node_modules/playwright-core/.local-browsers
      environment:
        PLAYWRIGHT_BROWSERS_PATH: /opt/playwright-env/browsers

    - name: Создать Python скрипт для автоматизации веб-интерфейса
      copy:
        content: |
          #!/usr/bin/env python3
          import asyncio
          import json
          import sys
          import os
          from datetime import datetime
          from playwright.async_api import async_playwright

          class XrayWebConfigurator:
              def __init__(self, panel_url, username, password, screenshots_dir):
                  self.panel_url = panel_url
                  self.username = username
                  self.password = password
                  self.screenshots_dir = screenshots_dir
                  self.browser = None
                  self.page = None
                  self.actions_log = []

              def log_action(self, action, status="success", details=""):
                  """Логирование действий"""
                  timestamp = datetime.now().isoformat()
                  log_entry = {
                      "timestamp": timestamp,
                      "action": action,
                      "status": status,
                      "details": details
                  }
                  self.actions_log.append(log_entry)
                  print(f"[{timestamp}] {action}: {status} - {details}")

              async def take_screenshot(self, name):
                  """Сделать скриншот"""
                  try:
                      screenshot_path = os.path.join(self.screenshots_dir, f"{name}_{datetime.now().strftime('%H%M%S')}.png")
                      await self.page.screenshot(path=screenshot_path, full_page=True)
                      self.log_action("Screenshot", "success", f"Saved to {screenshot_path}")
                      return screenshot_path
                  except Exception as e:
                      self.log_action("Screenshot", "error", str(e))
                      return None

              async def setup_browser(self):
                  """Настройка и запуск браузера"""
                  try:
                      playwright = await async_playwright().start()
                      self.browser = await playwright.chromium.launch(
                          headless=True,
                          args=[
                              '--no-sandbox',
                              '--disable-setuid-sandbox',
                              '--disable-dev-shm-usage',
                              '--disable-gpu',
                              '--no-first-run',
                              '--no-default-browser-check',
                              '--disable-default-apps'
                          ]
                      )
                      
                      context = await self.browser.new_context(
                          viewport={'width': 1920, 'height': 1080},
                          user_agent='Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
                      )
                      
                      self.page = await context.new_page()
                      
                      # Увеличиваем таймауты
                      self.page.set_default_timeout(30000)
                      self.page.set_default_navigation_timeout(30000)
                      
                      self.log_action("Browser setup", "success", "Chromium launched successfully")
                      return True
                      
                  except Exception as e:
                      self.log_action("Browser setup", "error", str(e))
                      return False

              async def login(self):
                  """Вход в панель"""
                  try:
                      self.log_action("Navigation", "info", f"Navigating to {self.panel_url}")
                      await self.page.goto(self.panel_url, wait_until='networkidle')
                      
                      await self.take_screenshot("01_initial_page")
                      
                      # Ищем поля для входа
                      username_selectors = [
                          'input[name="username"]',
                          'input[type="text"]',
                          '#username',
                          '.username',
                          'input[placeholder*="username" i]',
                          'input[placeholder*="user" i]'
                      ]
                      
                      password_selectors = [
                          'input[name="password"]',
                          'input[type="password"]',
                          '#password',
                          '.password'
                      ]
                      
                      username_field = None
                      password_field = None
                      
                      # Поиск поля username
                      for selector in username_selectors:
                          try:
                              username_field = await self.page.wait_for_selector(selector, timeout=5000)
                              if username_field:
                                  self.log_action("Find username field", "success", f"Found with selector: {selector}")
                                  break
                          except:
                              continue
                      
                      # Поиск поля password
                      for selector in password_selectors:
                          try:
                              password_field = await self.page.wait_for_selector(selector, timeout=5000)
                              if password_field:
                                  self.log_action("Find password field", "success", f"Found with selector: {selector}")
                                  break
                          except:
                              continue
                      
                      if not username_field or not password_field:
                          self.log_action("Login fields", "error", "Could not find username or password fields")
                          return False
                      
                      # Заполнение полей
                      await username_field.fill(self.username)
                      await password_field.fill(self.password)
                      
                      await self.take_screenshot("02_credentials_filled")
                      
                      # Поиск кнопки входа
                      login_selectors = [
                          'button[type="submit"]',
                          'input[type="submit"]',
                          'button:has-text("Login")',
                          'button:has-text("Sign in")',
                          'button:has-text("Enter")',
                          '.login-btn',
                          '#login-btn',
                          'form button'
                      ]
                      
                      login_button = None
                      for selector in login_selectors:
                          try:
                              login_button = await self.page.wait_for_selector(selector, timeout=5000)
                              if login_button:
                                  self.log_action("Find login button", "success", f"Found with selector: {selector}")
                                  break
                          except:
                              continue
                      
                      if not login_button:
                          # Попробуем нажать Enter в поле пароля
                          await password_field.press('Enter')
                          self.log_action("Login attempt", "info", "Pressed Enter in password field")
                      else:
                          await login_button.click()
                          self.log_action("Login attempt", "info", "Clicked login button")
                      
                      # Ждем перенаправления или изменения страницы
                      try:
                          await self.page.wait_for_load_state('networkidle', timeout=15000)
                          await asyncio.sleep(2)
                          
                          current_url = self.page.url
                          if current_url != self.panel_url or 'panel' in current_url.lower():
                              self.log_action("Login", "success", f"Redirected to: {current_url}")
                              await self.take_screenshot("03_login_success")
                              return True
                          else:
                              self.log_action("Login", "error", "No redirect detected, login may have failed")
                              await self.take_screenshot("03_login_failed")
                              return False
                              
                      except Exception as e:
                          self.log_action("Login verification", "error", str(e))
                          return False
                          
                  except Exception as e:
                      self.log_action("Login", "error", str(e))
                      await self.take_screenshot("03_login_error")
                      return False

              async def find_routing_section(self):
                  """Поиск раздела настройки маршрутизации"""
                  try:
                      # Возможные селекторы для навигации к настройкам маршрутизации
                      routing_nav_selectors = [
                          'a:has-text("Routing")',
                          'a:has-text("Route")',
                          'a:has-text("Rules")',
                          'a:has-text("Settings")',
                          'a:has-text("Config")',
                          'a:has-text("Xray")',
                          '[href*="routing"]',
                          '[href*="route"]',
                          '[href*="rules"]',
                          '[href*="config"]'
                      ]
                      
                      for selector in routing_nav_selectors:
                          try:
                              element = await self.page.wait_for_selector(selector, timeout=3000)
                              if element:
                                  await element.click()
                                  self.log_action("Navigate to routing", "success", f"Clicked: {selector}")
                                  await self.page.wait_for_load_state('networkidle')
                                  await self.take_screenshot("04_routing_section")
                                  return True
                          except:
                              continue
                      
                      self.log_action("Find routing section", "error", "Could not find routing navigation")
                      return False
                      
                  except Exception as e:
                      self.log_action("Find routing section", "error", str(e))
                      return False

              async def add_routing_rule(self, rule):
                  """Добавление правила маршрутизации"""
                  try:
                      # Поиск кнопки добавления правила
                      add_button_selectors = [
                          'button:has-text("Add")',
                          'button:has-text("+")',
                          'button:has-text("New")',
                          'button:has-text("Create")',
                          '.add-btn',
                          '.btn-add',
                          '#add-rule'
                      ]
                      
                      for selector in add_button_selectors:
                          try:
                              add_button = await self.page.wait_for_selector(selector, timeout=3000)
                              if add_button:
                                  await add_button.click()
                                  self.log_action("Add rule button", "success", f"Clicked: {selector}")
                                  await asyncio.sleep(1)
                                  break
                          except:
                              continue
                      
                      # Заполнение полей правила
                      if 'domain' in rule:
                          domain_selectors = [
                              'input[name*="domain"]',
                              'input[placeholder*="domain"]',
                              'textarea[name*="domain"]'
                          ]
                          
                          for selector in domain_selectors:
                              try:
                                  domain_field = await self.page.wait_for_selector(selector, timeout=3000)
                                  if domain_field:
                                      await domain_field.fill(rule['domain'])
                                      self.log_action("Fill domain", "success", f"Domain: {rule['domain']}")
                                      break
                              except:
                                  continue
                      
                      if 'ip' in rule:
                          ip_selectors = [
                              'input[name*="ip"]',
                              'input[placeholder*="ip"]',
                              'textarea[name*="ip"]'
                          ]
                          
                          for selector in ip_selectors:
                              try:
                                  ip_field = await self.page.wait_for_selector(selector, timeout=3000)
                                  if ip_field:
                                      await ip_field.fill(rule['ip'])
                                      self.log_action("Fill IP", "success", f"IP: {rule['ip']}")
                                      break
                              except:
                                  continue
                      
                      # Установка outbound тега
                      outbound_selectors = [
                          'select[name*="outbound"]',
                          'select[name*="tag"]',
                          'input[name*="outbound"]',
                          'input[name*="tag"]'
                      ]
                      
                      for selector in outbound_selectors:
                          try:
                              outbound_field = await self.page.wait_for_selector(selector, timeout=3000)
                              if outbound_field:
                                  tag_name = await outbound_field.tag_name()
                                  if tag_name.lower() == 'select':
                                      await outbound_field.select_option(rule['outbound'])
                                  else:
                                      await outbound_field.fill(rule['outbound'])
                                  self.log_action("Set outbound", "success", f"Outbound: {rule['outbound']}")
                                  break
                          except:
                              continue
                      
                      # Сохранение правила
                      save_selectors = [
                          'button:has-text("Save")',
                          'button:has-text("OK")',
                          'button:has-text("Apply")',
                          'button:has-text("Confirm")',
                          '.btn-save',
                          '.btn-ok'
                      ]
                      
                      for selector in save_selectors:
                          try:
                              save_button = await self.page.wait_for_selector(selector, timeout=3000)
                              if save_button:
                                  await save_button.click()
                                  self.log_action("Save rule", "success", f"Rule saved: {rule['comment']}")
                                  await asyncio.sleep(2)
                                  return True
                          except:
                              continue
                      
                      self.log_action("Save rule", "error", f"Could not save rule: {rule['comment']}")
                      return False
                      
                  except Exception as e:
                      self.log_action("Add routing rule", "error", f"{rule['comment']}: {str(e)}")
                      return False

              async def configure_routing(self, rules):
                  """Настройка всех правил маршрутизации"""
                  try:
                      if not await self.setup_browser():
                          return False
                      
                      if not await self.login():
                          return False
                      
                      if not await self.find_routing_section():
                          return False
                      
                      success_count = 0
                      for i, rule in enumerate(rules):
                          self.log_action("Processing rule", "info", f"Rule {i+1}/{len(rules)}: {rule['comment']}")
                          
                          if await self.add_routing_rule(rule):
                              success_count += 1
                              await self.take_screenshot(f"05_rule_{i+1}_added")
                          else:
                              await self.take_screenshot(f"05_rule_{i+1}_failed")
                      
                      await self.take_screenshot("06_final_configuration")
                      
                      self.log_action("Configuration complete", "success", f"Added {success_count}/{len(rules)} rules")
                      
                      return success_count > 0
                      
                  except Exception as e:
                      self.log_action("Configure routing", "error", str(e))
                      return False
                  finally:
                      if self.browser:
                          await self.browser.close()

              def get_actions_log(self):
                  """Получить лог действий"""
                  return self.actions_log

          async def main():
              if len(sys.argv) != 6:
                  print("Usage: script.py <panel_url> <username> <password> <rules_file> <screenshots_dir>")
                  sys.exit(1)
              
              panel_url = sys.argv[1]
              username = sys.argv[2]
              password = sys.argv[3]
              rules_file = sys.argv[4]
              screenshots_dir = sys.argv[5]
              
              # Читаем правила
              with open(rules_file, 'r', encoding='utf-8') as f:
                  rules = json.load(f)
              
              # Создаем конфигуратор
              configurator = XrayWebConfigurator(panel_url, username, password, screenshots_dir)
              
              # Выполняем настройку
              success = await configurator.configure_routing(rules)
              
              # Сохраняем лог действий
              log_file = os.path.join(screenshots_dir, 'actions_log.json')
              with open(log_file, 'w', encoding='utf-8') as f:
                  json.dump(configurator.get_actions_log(), f, indent=2, ensure_ascii=False)
              
              print(f"Actions log saved to: {log_file}")
              
              if success:
                  print("Configuration completed successfully")
                  sys.exit(0)
              else:
                  print("Configuration failed")
                  sys.exit(1)

          if __name__ == "__main__":
              asyncio.run(main())
        dest: /tmp/xray_web_configurator.py
        mode: "0755"

    - name: Создать файл с правилами маршрутизации для веб-конфигуратора
      copy:
        content: "{{ routing_rules | to_nice_json }}"
        dest: /tmp/web_routing_rules.json
        mode: "0644"

    - name: Запустить веб-конфигурацию с Playwright
      shell: |
        export DISPLAY=:99
        Xvfb :99 -screen 0 1920x1080x24 > /dev/null 2>&1 &
        XVFB_PID=$!
        sleep 3

        source /opt/playwright-env/bin/activate
        export PLAYWRIGHT_BROWSERS_PATH=/opt/playwright-env/browsers
        export PYTHONUNBUFFERED=1

        timeout 240 python3 /tmp/xray_web_configurator.py \
          "{{ panel_url }}" \
          "{{ panel_username }}" \
          "{{ panel_password }}" \
          /tmp/web_routing_rules.json \
          "{{ screenshots_dir }}" 2>&1

        RESULT=$?
        kill $XVFB_PID 2>/dev/null || true
        exit $RESULT
      register: playwright_result
      ignore_errors: yes

    - name: Показать результат выполнения Playwright
      debug:
        msg:
          - "🎭 Результат веб-автоматизации:"
          - "📊 Код возврата: {{ playwright_result.rc | default('N/A') }}"
          - "📋 Вывод:"
          - "{{ playwright_result.stdout_lines | default(['Нет вывода']) }}"
          - "{% if playwright_result.stderr_lines %}❌ Ошибки: {{ playwright_result.stderr_lines }}{% endif %}"

    - name: Проверить созданные скриншоты
      find:
        paths: "{{ screenshots_dir }}"
        patterns: "*.png"
      register: screenshots_found

    - name: Показать информацию о скриншотах
      debug:
        msg:
          - "📸 Создано скриншотов: {{ screenshots_found.files | length }}"
          - "📁 Директория: {{ screenshots_dir }}"
          - "{% for screenshot in screenshots_found.files %}"
          - "  • {{ screenshot.path | basename }}"
          - "{% endfor %}"

    - name: Прочитать лог действий
      slurp:
        src: "{{ screenshots_dir }}/actions_log.json"
      register: actions_log_raw
      ignore_errors: yes

    - name: Парсинг лога действий
      set_fact:
        actions_log: "{{ actions_log_raw.content | b64decode | from_json }}"
      when: actions_log_raw.content is defined

    - name: Создать отчет о веб-автоматизации
      copy:
        content: |
          ==========================================
          🎭 ОТЧЕТ О ВЕБ-АВТОМАТИЗАЦИИ XRAY
          ==========================================

          📅 Дата: {{ ansible_date_time.iso8601 }}
          🌐 Сервер: {{ server_ip }}
          📡 URL панели: {{ panel_url }}

          ==========================================
          🔧 РЕЗУЛЬТАТ ВЫПОЛНЕНИЯ
          ==========================================

          Статус: {{ 'Успешно' if playwright_result.rc == 0 else 'Ошибка' }}
          Код возврата: {{ playwright_result.rc | default('N/A') }}
          Создано скриншотов: {{ screenshots_found.files | length }}

          ==========================================
          📋 ВЫВОД PLAYWRIGHT
          ==========================================

          {{ playwright_result.stdout | default('Нет вывода') }}

          {% if playwright_result.stderr %}
          ==========================================
          ❌ ОШИБКИ
          ==========================================

          {{ playwright_result.stderr }}
          {% endif %}

          ==========================================
          📸 СКРИНШОТЫ
          ==========================================

          {% for screenshot in screenshots_found.files %}
          {{ screenshot.path | basename }} ({{ screenshot.size }} байт)
          {% endfor %}

          ==========================================
          📊 ЛОГ ДЕЙСТВИЙ
          ==========================================

          {% if actions_log is defined %}
          {% for action in actions_log %}
          [{{ action.timestamp }}] {{ action.action }}: {{ action.status }}
          {% if action.details %}  Детали: {{ action.details }}{% endif %}

          {% endfor %}
          {% else %}
          Лог действий недоступен
          {% endif %}

          ==========================================
          ✅ ДОБАВЛЕННЫЕ ПРАВИЛА
          ==========================================

          {% for rule in routing_rules %}
          {{ loop.index }}. {{ rule.comment }}
          {% if rule.domain is defined %}   Домен: {{ rule.domain }} → {{ rule.outbound }}{% endif %}
          {% if rule.ip is defined %}   IP: {{ rule.ip }} → {{ rule.outbound }}{% endif %}

          {% endfor %}

          ==========================================
          💡 ПРЕИМУЩЕСТВА ВЕБ-АВТОМАТИЗАЦИИ
          ==========================================

          • Использует реальный веб-интерфейс
          • Работает с любой версией 3x-ui
          • Визуальное подтверждение действий
          • Обходит ограничения API и файлового доступа
          • Полная совместимость с панелью управления

          ==========================================
        dest: "{{ backup_dir }}/PLAYWRIGHT_REPORT.txt"
        mode: "0644"

    - name: Удалить временные файлы
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /tmp/xray_web_configurator.py
        - /tmp/web_routing_rules.json

    - name: Показать финальный результат
      debug:
        msg:
          - "🎭 Веб-автоматизация завершена!"
          - ""
          - "📊 Результат: {{ 'Успешно' if playwright_result.rc == 0 else 'Ошибка' }}"
          - "📸 Скриншотов: {{ screenshots_found.files | length }}"
          - "📁 Директория скриншотов: {{ screenshots_dir }}"
          - ""
          - "✅ Правила для добавления:"
          - "  • 🏛️ geosite:category-gov-ru → direct"
          - "  • 🇷🇺 regexp:.*\\.ru$ → direct"
          - "  • 🤖 geosite:openai → direct"
          - "  • 🌐 geoip:ru → direct"
          - ""
          - "📋 Отчет: {{ backup_dir }}/PLAYWRIGHT_REPORT.txt"
          - "📊 Лог действий: {{ screenshots_dir }}/actions_log.json"
